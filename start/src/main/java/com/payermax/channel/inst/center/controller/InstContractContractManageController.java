package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.dto.contract.InstContractVersionInfoDto;
import com.payermax.channel.inst.center.app.manage.contract.InstContractManageService;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.request.contract.InstContractAccountingTypeModifyRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractAiParseQueryRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractInfoQueryRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractVersionUpgradeRequest;
import com.payermax.channel.inst.center.app.response.InstContractAiParseQueryResponse;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.utils.ExceptionUtils;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.model.dto.response.RowResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/9
 * @DESC 机构合约-合同管理
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("instCenter/contract/contractManage")
public class InstContractContractManageController {

    private final InstContractManageService instContractManageService;


    /**
     * 查询合约基本信息
     */
    @PostMapping("queryInstContractInfo")
    public Result<RowResponse<InstContractVersionInfoDto>> queryInstContractInfo(@RequestBody InstContractInfoQueryRequest request){
        return ExceptionUtils.commonTryCatch(() -> instContractManageService.queryInstContractInfo(request));
    }


    /**
     * 修改渠道核对方式
     */
    @PostMapping("updateAccountingType")
    public Result<Boolean> updateAccountingType(@RequestBody InstContractAccountingTypeModifyRequest request, @RequestHeader("shareId") String shareId){
        AssertUtil.isTrue(StringUtils.isNotBlank(shareId), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "shareId不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getContractVersion()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "渠道版本号不能为空");
        AssertUtil.isTrue(MapUtils.isNotEmpty(request.getAccountingTypeJson()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "渠道核对方式不能为空");

        return ExceptionUtils.commonTryCatch(() -> instContractManageService.updateAccountingType(request,shareId));
    }

    /**
     * 合约版本升级
     */
    @PostMapping("contractVersionUpgrade")
    public Result<Boolean> contractVersionUpgrade(@RequestBody InstContractVersionUpgradeRequest request, @RequestHeader("shareId") String shareId){
        AssertUtil.isTrue(StringUtils.isNotBlank(shareId), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "shareId不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInstCode()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "机构编码不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getContractEntity()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "我司主体不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInstProductType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "合约类型不能为空");
        AssertUtil.isTrue(Objects.nonNull(request.getEffectiveStartTime()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "生效开始时间不能为空");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(request.getFeeList()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "合约费用不能为空");
        return ExceptionUtils.commonTryCatch(() -> {
            instContractManageService.contractVersionUpgrade(request, shareId);
            return Boolean.TRUE;
        });
    }

    /**
     * 下载合约模板
     */
    @PostMapping("/contractTemplateDownload")
    public Result<String> contractTemplateDownload(@RequestParam("contractBizType") String contractBizType) {
        AssertUtil.isTrue(StringUtils.isNotBlank(contractBizType), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "合约业务类型不能为空");
        return ExceptionUtils.commonTryCatch(() -> instContractManageService.contractTemplateDownload(contractBizType));
    }

    /**
     * 机构合约-智能解析
     */
    @PostMapping("contractAiParse")
    public Result<String> contractAiParse(@RequestBody InstContractParseRequestDTO request, @RequestHeader("shareId") String shareId) {
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInstCode()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "机构编码不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getContractEntity()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "我司主体不能为空");
        AssertUtil.isTrue(Objects.nonNull(request.getEffectiveTime()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "生效时间不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getContractFeeFile()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "合约文件地址不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(shareId), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "shareId不能为空");
        request.setOperator(shareId);
        return ExceptionUtils.commonTryCatch(() -> instContractManageService.contractAiParse(request));
    }


    @PostMapping("listAiParse")
    public Result<InstContractAiParseQueryResponse> getAiParseList(@RequestBody InstContractAiParseQueryRequest request) {
        return Collections.emptyList();
    }

}
