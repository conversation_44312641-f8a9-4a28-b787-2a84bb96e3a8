<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstContractDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity">
    <id column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="real_contract_no" jdbcType="VARCHAR" property="realContractNo" />
    <result column="inst_id" jdbcType="BIGINT" property="instId" />
    <result column="apply_no" jdbcType="VARCHAR" property="applyNo" />
    <result column="funds_settle_inst" jdbcType="VARCHAR" property="fundsSettleInst" />
    <result column="service_purchaser" jdbcType="VARCHAR" property="servicePurchaser" />
    <result column="service_provider" jdbcType="VARCHAR" property="serviceProvider" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="sign_flag" jdbcType="VARCHAR" property="signFlag" />
    <result column="contract_attach_id" jdbcType="VARCHAR" property="contractAttachId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity">
    insert into tb_inst_contract (contract_no,real_contract_no, inst_id, apply_no,
      funds_settle_inst, service_purchaser, service_provider, 
      start_date, end_date, sign_flag, 
      contract_attach_id, remark, status)
    values (#{contractNo,jdbcType=VARCHAR},#{realContractNo,jdbcType=VARCHAR}, #{instId,jdbcType=BIGINT}, #{applyNo,jdbcType=VARCHAR},
      #{fundsSettleInst,jdbcType=VARCHAR}, #{servicePurchaser,jdbcType=VARCHAR}, #{serviceProvider,jdbcType=VARCHAR}, 
      #{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, #{signFlag,jdbcType=VARCHAR},
      #{contractAttachId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR})
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstContractEntity">
    update tb_inst_contract
    <set>
      <if test="realContractNo != null and realContractNo != ''">
        real_contract_no = #{realContractNo,jdbcType=VARCHAR},
      </if>
      <if test="fundsSettleInst != null and fundsSettleInst != ''">
        funds_settle_inst = #{fundsSettleInst,jdbcType=VARCHAR},
      </if>
      <if test="servicePurchaser != null and servicePurchaser != ''">
        service_purchaser = #{servicePurchaser,jdbcType=VARCHAR},
      </if>
      <if test="serviceProvider != null and serviceProvider != ''">
        service_provider = #{serviceProvider,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null ">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null ">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="signFlag != null and signFlag != ''">
        sign_flag = #{signFlag,jdbcType=VARCHAR},
      </if>
      <if test="contractAttachId != null and contractAttachId != ''">
        contract_attach_id = #{contractAttachId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        status = #{status,jdbcType=VARCHAR},
      </if>
    </set>
    where contract_no = #{contractNo,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select contract_no,real_contract_no, inst_id, apply_no, funds_settle_inst, service_purchaser, service_provider,
    start_date, end_date, sign_flag, contract_attach_id, remark, status, utc_create, 
    utc_modified
    from tb_inst_contract
    where 1=1
    <if test="contractNo != null and contractNo != ''">
      and contract_no = #{contractNo,jdbcType=VARCHAR}
    </if>
    <if test="realContractNo != null and realContractNo != ''">
      and real_contract_no = #{realContractNo,jdbcType=VARCHAR}
    </if>
    <if test="applyNo != null and applyNo != ''">
      and apply_no = #{applyNo,jdbcType=VARCHAR}
    </if>
    <if test="instId != null">
      and inst_id = #{instId,jdbcType=BIGINT}
    </if>
    order by utc_modified desc
  </select>
  <select id="selectByInstIds" resultMap="BaseResultMap">
    select contract_no,real_contract_no, inst_id, apply_no, funds_settle_inst, service_purchaser, service_provider,
    start_date, end_date, sign_flag, contract_attach_id, remark, status, utc_create,
    utc_modified
    from tb_inst_contract
    where inst_id in
    <foreach collection="instIds" index="index" item="instId" open="(" separator="," close=")">
      #{instId}
    </foreach>
  </select>
</mapper>