<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstProductCapabilityDao">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity">
    <id column="capability_code" jdbcType="VARCHAR" property="capabilityCode" />
    <result column="inst_product_code" jdbcType="VARCHAR" property="instProductCode" />
    <result column="target_org" jdbcType="VARCHAR" property="targetOrg" />
    <result column="card_org" jdbcType="VARCHAR" property="cardOrg" />
    <result column="country" jdbcType="CHAR" property="country" />
    <result column="currency" jdbcType="CHAR" property="currency" />
    <result column="customer_type" jdbcType="CHAR" property="customerType" />
    <result column="amount_multiple_limit" jdbcType="VARCHAR" property="amountMultipleLimit" />
    <result column="amount_single_limit" jdbcType="VARCHAR" property="amountSingleLimit" />
    <result column="amount_day_limit" jdbcType="VARCHAR" property="amountDayLimit" />
    <result column="amount_month_limit" jdbcType="VARCHAR" property="amountMonthLimit" />
    <result column="payment_tool" jdbcType="VARCHAR" property="paymentTool" />
    <result column="payment_flow" jdbcType="VARCHAR" property="paymentFlow" />
    <result column="available_time" jdbcType="VARCHAR" property="availableTime" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="extra_info" jdbcType="LONGVARCHAR" property="extraInfo" />
    <result column="settlement_extra_info" jdbcType="LONGVARCHAR" property="settlementExtraInfo" />
  </resultMap>
  <insert id="insert" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity">
    insert into tb_inst_product_capability (capability_code, inst_product_code,
      target_org, card_org, country,
      currency, amount_multiple_limit ,amount_single_limit, amount_day_limit,
      amount_month_limit, payment_tool,customer_type, payment_flow,
      available_time, extra_info,settlement_extra_info)
    values (#{capabilityCode,jdbcType=VARCHAR}, #{instProductCode,jdbcType=VARCHAR},
      #{targetOrg,jdbcType=VARCHAR}, #{cardOrg,jdbcType=VARCHAR}, #{country,jdbcType=CHAR},
      #{currency,jdbcType=CHAR},#{amountMultipleLimit,jdbcType=VARCHAR}, #{amountSingleLimit,jdbcType=VARCHAR}, #{amountDayLimit,jdbcType=VARCHAR},
      #{amountMonthLimit,jdbcType=VARCHAR}, #{paymentTool,jdbcType=VARCHAR},#{customerType,jdbcType=VARCHAR}, #{paymentFlow,jdbcType=VARCHAR},
      #{availableTime,jdbcType=VARCHAR}, #{extraInfo,jdbcType=LONGVARCHAR}, #{settlementExtraInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertBatch">
    insert into tb_inst_product_capability (capability_code, inst_product_code,
      target_org, card_org, country,
      currency,amount_multiple_limit, amount_single_limit, amount_day_limit,
      amount_month_limit, payment_tool,customer_type, payment_flow,
      available_time, extra_info, settlement_extra_info)
    values
    <foreach collection="records" index="index" item="item" separator=",">
      (#{item.capabilityCode,jdbcType=VARCHAR}, #{item.instProductCode,jdbcType=VARCHAR},
      #{item.targetOrg,jdbcType=VARCHAR}, #{item.cardOrg,jdbcType=VARCHAR}, #{item.country,jdbcType=CHAR},
      #{item.currency,jdbcType=CHAR},#{item.amountMultipleLimit,jdbcType=VARCHAR}, #{item.amountSingleLimit,jdbcType=VARCHAR}, #{item.amountDayLimit,jdbcType=VARCHAR},
      #{item.amountMonthLimit,jdbcType=VARCHAR}, #{item.paymentTool,jdbcType=VARCHAR},#{item.customerType,jdbcType=VARCHAR}, #{item.paymentFlow,jdbcType=VARCHAR},
      #{item.availableTime,jdbcType=VARCHAR}, #{item.extraInfo,jdbcType=LONGVARCHAR},#{item.settlementExtraInfo,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity">
    update tb_inst_product_capability
    <set>
      <if test="targetOrg != null and targetOrg != ''">
        target_org = #{targetOrg,jdbcType=VARCHAR},
      </if>
      <if test="cardOrg != null and cardOrg != ''">
        card_org = #{cardOrg,jdbcType=VARCHAR},
      </if>
      <if test="country != null and country != ''">
        country = #{country,jdbcType=CHAR},
      </if>
      <if test="currency != null and currency != ''">
        currency = #{currency,jdbcType=CHAR},
      </if>
      <if test="amountMultipleLimit != null and amountMultipleLimit != ''">
        amount_multiple_limit = #{amountMultipleLimit,jdbcType=VARCHAR},
      </if>
      <if test="amountSingleLimit != null and amountSingleLimit != ''">
        amount_single_limit = #{amountSingleLimit,jdbcType=VARCHAR},
      </if>
      <if test="amountDayLimit != null and amountDayLimit != ''">
        amount_day_limit = #{amountDayLimit,jdbcType=VARCHAR},
      </if>
      <if test="amountMonthLimit != null and amountMonthLimit != ''">
        amount_month_limit = #{amountMonthLimit,jdbcType=VARCHAR},
      </if>
      <if test="paymentTool != null and paymentTool != ''">
        payment_tool = #{paymentTool,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null and customerType != ''">
        customer_type = #{customerType,jdbcType=VARCHAR},
      </if>
      <if test="paymentFlow != null and paymentFlow != ''">
        payment_flow = #{paymentFlow,jdbcType=VARCHAR},
      </if>
      <if test="availableTime != null and availableTime != ''">
        available_time = #{availableTime,jdbcType=VARCHAR},
      </if>
      <if test="extraInfo != null and extraInfo != ''">
        extra_info = #{extraInfo,jdbcType=LONGVARCHAR},
      </if>
      <if test="settlementExtraInfo != null and settlementExtraInfo != ''">
        settlement_extra_info = #{settlementExtraInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where capability_code = #{capabilityCode,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultMap="BaseResultMap">
    select capability_code, inst_product_code, target_org, card_org, country, currency,
    amount_multiple_limit,amount_single_limit, amount_day_limit, amount_month_limit, payment_tool,customer_type, payment_flow,
    available_time, utc_create, utc_modified, extra_info,settlement_extra_info
    from tb_inst_product_capability
    where 1=1
    <if test="productCodes != null and productCodes.size() > 0">
      and inst_product_code in
      <foreach collection="productCodes" index="index" item="productCode" open="(" separator="," close=")">
        #{productCode}
      </foreach>
    </if>
    <if test="instProductCode != null and instProductCode != ''">
      and inst_product_code = #{instProductCode,jdbcType=VARCHAR}
    </if>
    <if test="capabilityCodes != null and capabilityCodes.size() > 0">
      and capability_code in
      <foreach collection="capabilityCodes" index="index" item="capabilityCode" open="(" separator="," close=")">
        #{capabilityCode}
      </foreach>
    </if>
    <if test="country != null and country != ''">
      and country = #{country,jdbcType=VARCHAR}
    </if>
    <if test="currency != null and currency != ''">
      and currency = #{currency,jdbcType=VARCHAR}
    </if>
    <if test="customerType != null and customerType != ''">
      and customer_type = #{customerType,jdbcType=VARCHAR}
    </if>
    <if test="targetOrgs != null and targetOrgs.size() > 0">
      and target_org in
      <foreach collection="targetOrgs" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="cardOrgs != null and cardOrgs.size() > 0">
      and card_org in
      <foreach collection="cardOrgs" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    order by utc_modified desc
  </select>

  <select id="selectByProductCodeAndVersion" resultMap="BaseResultMap">
    select capability_code, inst_product_code, target_org, card_org, country, currency,
    amount_multiple_limit,amount_single_limit, amount_day_limit, amount_month_limit, payment_tool,customer_type ,payment_flow,
    available_time, utc_create, utc_modified, extra_info,settlement_extra_info
    from tb_inst_product_capability
    where inst_product_code = #{productCode,jdbcType=VARCHAR}
    and version = #{version,jdbcType=VARCHAR}
    order by utc_modified desc
  </select>

  <select id="selectByProductCodeAndCapabilityCode" resultMap="BaseResultMap">
    select capability_code, inst_product_code, target_org, card_org, country, currency,
    amount_multiple_limit,amount_single_limit, amount_day_limit, amount_month_limit, payment_tool,customer_type, payment_flow,
    available_time, utc_create, utc_modified, extra_info,settlement_extra_info
    from tb_inst_product_capability
    where inst_product_code = #{productCode,jdbcType=VARCHAR}
    and capability_code in
    <foreach collection="capabilityCodes" index="index" item="capabilityCode" open="(" separator="," close=")">
      #{capabilityCode}
    </foreach>
  </select>


  <select id="selectByCapabilityCodes" resultMap="BaseResultMap">
    select capability_code, inst_product_code, target_org, card_org, country, currency,
    amount_multiple_limit,amount_single_limit, amount_day_limit, amount_month_limit, payment_tool,customer_type, payment_flow,
    available_time, utc_create, utc_modified, extra_info,settlement_extra_info
    from tb_inst_product_capability
    where capability_code in
    <foreach collection="capabilityCodes" index="index" item="capabilityCode" open="(" separator="," close=")">
      #{capabilityCode}
    </foreach>
  </select>

  <delete id="deleteByProductCode">
    delete from tb_inst_product_capability
    where inst_product_code = #{productCode,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteByProductCodeAndVersion">
    delete from tb_inst_product_capability
    where inst_product_code = #{productCode,jdbcType=VARCHAR}
    and version = #{version,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteByProductCodeAndCapabilityCode">
    delete from tb_inst_product_capability
    where inst_product_code = #{productCode,jdbcType=VARCHAR}
    and capability_code in
    <foreach collection="capabilityCodes" index="index" item="capabilityCode" open="(" separator="," close=")">
      #{capabilityCode}
    </foreach>
  </delete>
</mapper>
