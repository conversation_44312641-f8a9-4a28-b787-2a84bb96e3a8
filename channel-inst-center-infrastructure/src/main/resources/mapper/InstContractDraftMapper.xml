<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.channel.inst.center.infrastructure.mapper.InstContractDraftMapper">
  <resultMap id="BaseResultMap" type="com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft">
    <!--@mbg.generated-->
    <!--@Table tb_inst_contract_draft-->
    <result column="draft_id" jdbcType="VARCHAR" property="draftId" />
    <result column="draft_data" jdbcType="LONGVARCHAR" property="draftData" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="action_type" jdbcType="VARCHAR" property="actionType" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="inst_code" jdbcType="VARCHAR" property="instCode" />
    <result column="contract_entity" jdbcType="VARCHAR" property="contractEntity" />
    <result column="inst_product_name" jdbcType="VARCHAR" property="instProductName" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="extend_fields" jdbcType="LONGVARCHAR" property="extendFields" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    draft_id, draft_data, business_type, action_type, `owner`, inst_code, contract_entity, 
    inst_product_name, contract_no, effective_time, `operator`, business_key, extend_fields, 
    `status`, retry_count, utc_create, utc_modified
  </sql>

</mapper>