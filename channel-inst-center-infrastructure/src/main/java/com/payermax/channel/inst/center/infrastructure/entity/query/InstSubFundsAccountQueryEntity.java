package com.payermax.channel.inst.center.infrastructure.entity.query;

import lombok.Data;

import java.util.Date;

/**
 * 机构子级资金账号表
 *
 * <AUTHOR>
 */
@Data
public class InstSubFundsAccountQueryEntity {
    /**
     * 子级账号标识
     */
    private String subAccountId;
    /**
     * 业务申请唯一键
     */
    private String businessKey;
    /**
     * 机构帐号标识
     */
    private String accountId;

    /**
     * 子级机构账号创建总数标识
     */
    private Long bucketId;

    /**
     * 机构帐号号段标识
     */
    private String numberSegmentId;
    /**
     * 用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT
     */
    private String subUseType;
    /**
     * 子级账号号码
     */
    private String subAccountNo;

    /**
     * 主账号号码
     */
    private String accountNo;
    /**
     * 子级账号名称
     */
    private String subAccountName;
    /**
     * 机构子账号BBAN
     */
    private String bSubAccountNo;
    /**
     * 申请子级账号的商户号
     */
    private String merchantNo;
    /**
     * 状态 0：未激活，1：激活中，2：已激活，3：已停用，4：申请中，9：初始化
     */
    private Integer status;
    /**
     * 账号相关拓展信息，json格式
     */
    private String accountJson;
    /**
     * 创建时间
     */
    private Date utcCreate;
    /**
     * 更新时间
     */
    private Date utcModified;

}