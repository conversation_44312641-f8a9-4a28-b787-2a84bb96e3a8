package com.payermax.channel.inst.center.infrastructure.util;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.google.common.base.Supplier;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

/**
 * 数据库实例迁移工具类
 * <AUTHOR>
 */
@Slf4j
@Component
public class MigrationAlgorithmUtils {

    public static final String MASTER = "master";

    public static final String NEW_MASTER = "new-master";

    private static Boolean MIGRATE_START = false;

    @NacosValue(value = "${database.migrate.pay.start}", autoRefreshed = true)
    public void setMigrateStart(Boolean migrateStart) {
        MigrationAlgorithmUtils.MIGRATE_START = migrateStart;
        log.info("database.migrate.pay.start:{}",migrateStart);
    }

    public static Boolean getMigrateStart() {
        return MigrationAlgorithmUtils.MIGRATE_START;
    }
    
    public static <T> T selectHandle(Supplier<T> selectFunction, Predicate<T> predicate) {
        T t = null;
        try {
            if (!MIGRATE_START) {
                DynamicDataSourceContextHolder.push(MigrationAlgorithmUtils.MASTER);
                t = selectFunction.get();
            } else {
                DynamicDataSourceContextHolder.push(MigrationAlgorithmUtils.NEW_MASTER);
                t = selectFunction.get();
                if (predicate.test(t)) {
                    DynamicDataSourceContextHolder.push(MigrationAlgorithmUtils.MASTER);
                    t = selectFunction.get();
                }
            }
        } finally {
            log.info("selectHandle 数据源:{}", DynamicDataSourceContextHolder.peek());
            DynamicDataSourceContextHolder.poll();
        }
        return t;
    }
}
