package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.entity.ChannelMethodEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.ChannelMethodDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> tracy
 * @version 2022-10-20 11:52 PM
 */
@Service
public class ChannelMethodCacheManager extends AbstractLocalCacheManager {

    @Resource
    private ChannelMethodDao channelMethodDao;

    private static Map<String, ChannelMethodEntity> CHANNEL_METHOD_CACHE = new ConcurrentHashMap<>();

    @Override
    public void doInit() {
        List<ChannelMethodEntity> channelMethodEntities = channelMethodDao.selectAll(new ChannelMethodEntity());
        channelMethodEntities.forEach(item -> CHANNEL_METHOD_CACHE.put(item.getChannelMethodCode(), item));
    }

    @Override
    public void doRefresh() {
        List<ChannelMethodEntity> channelMethodEntities = channelMethodDao.selectAll(new ChannelMethodEntity());
        Map<String, ChannelMethodEntity> temp = new ConcurrentHashMap<>();
        channelMethodEntities.forEach(item -> temp.put(item.getChannelMethodCode(), item));
        CHANNEL_METHOD_CACHE = temp;
    }

    /**
     * 根据channelMethodCode获取ChannelMethodEntity
     */
    public ChannelMethodEntity getChannelMethodByChannelMethodCode(String channelMethodCode) {
        return CHANNEL_METHOD_CACHE.get(channelMethodCode);
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.CHANNEL_METHOD;
    }
}
