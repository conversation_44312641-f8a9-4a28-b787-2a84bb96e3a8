package com.payermax.channel.inst.center.infrastructure.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 2022-10-20 11:50 PM
 */
@Service
@Slf4j
public class CacheStarter implements InitializingBean {

    @Override
    public void afterPropertiesSet() {
        for (CacheEnum cacheEnum : CacheEnum.values()) {
            CacheRegistry.getCacheManager(cacheEnum).doInit();
            log.info("CacheStarter cacheCode:[{}] init success!", cacheEnum.name());
        }
    }
}
