package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.google.common.base.Joiner;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstProductDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> tracy
 * @version 2022-10-21 3:17 PM
 */
@Service
@Slf4j
public class InstProductCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstProductDao instProductDao;

    private static Map<String, InstProductEntity> CACHE_MAP = new ConcurrentHashMap<>();
    private static Map<String, InstProductEntity> PRODUCT_CACHE_MAP = new ConcurrentHashMap<>();

    @Override
    public void doInit() {
        List<InstProductEntity> instProductEntities = instProductDao.selectAll(new InstProductEntity());
        instProductEntities.forEach(item -> CACHE_MAP.put(item.getProductCode(), item));
        instProductEntities.forEach(item -> PRODUCT_CACHE_MAP.put(Joiner.on("_").join(item.getProductCode(),item.getChannelType(),item.getPaymentMethodType()), item));
    }

    @Override
    public void doRefresh() {
        Map<String, InstProductEntity> temp = new ConcurrentHashMap<>();
        List<InstProductEntity> instProductEntities = instProductDao.selectAll(new InstProductEntity());
        instProductEntities.forEach(item -> temp.put(item.getProductCode(), item));
        Map<String, InstProductEntity> map = new ConcurrentHashMap<>();
        instProductEntities.forEach(item -> map.put(Joiner.on("_").join(item.getProductCode(),item.getChannelType(),item.getPaymentMethodType()), item));
        CACHE_MAP = temp;
        PRODUCT_CACHE_MAP = map;
    }

    public List<InstProductEntity> getByProductCodes(List<String> productCodes) {
        List<InstProductEntity> response = new ArrayList<>(productCodes.size());
        productCodes.forEach(item -> {
            InstProductEntity instProductEntity = CACHE_MAP.get(item);
            if (instProductEntity != null) {
                response.add(instProductEntity);
            }
        });

        return response;
    }

    public List<InstProductEntity> getByChannelTypeAndPaymentMethodType(List<String> productCodes,String channelType,String paymentMethodType) {
        List<InstProductEntity> response = new ArrayList<>(productCodes.size());
        productCodes.forEach(item -> {
            InstProductEntity instProductEntity = PRODUCT_CACHE_MAP.get(Joiner.on("_").join(item,channelType,paymentMethodType));
            if (instProductEntity != null) {
                response.add(instProductEntity);
            }
        });
        return response;
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_PRODUCT;
    }
}
