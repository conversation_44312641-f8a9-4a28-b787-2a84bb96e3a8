package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstContactEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstContactDao {

    /**
     * 插入记录
     *
     * @param record
     * @return
     */
    int insert(InstContactEntity record);

    /**
     * 查询记录
     *
     * @param instContactEntity
     * @return
     */
    List<InstContactEntity> selectAll(InstContactEntity instContactEntity);

    /**
     * 查询记录
     *
     * @param instIds
     * @return
     */
    List<InstContactEntity> selectByInstIds(@Param("instIds") List<Long> instIds);

    /**
     * 更新记录
     *
     * @param record
     * @return
     */
    int updateByPrimaryKey(InstContactEntity record);
}