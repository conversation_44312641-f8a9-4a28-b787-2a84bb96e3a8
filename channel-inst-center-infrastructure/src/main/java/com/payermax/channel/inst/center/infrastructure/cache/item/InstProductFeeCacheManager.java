package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.google.common.base.Joiner;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductFeeEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstProductFeeDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> tracy
 * @version 2022-10-21 3:24 PM
 */
@Service
@Slf4j
public class InstProductFeeCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstProductFeeDao instProductFeeDao;

    //---一个产品其实理论上是可能有多个FEE的,这里模型有点问题,所以用list
    private Map<String, List<InstProductFeeEntity>> CACHE_MAP = new ConcurrentHashMap<>();
    private Map<String, List<InstProductFeeEntity>> CONTRACT_PRODUCT_CACHE_MAP = new ConcurrentHashMap<>();
    private Map<String, InstProductFeeEntity> CONTRACT_PRODUCT_CAPABILITY_CACHE_MAP = new ConcurrentHashMap<>();

    @Override
    public void doInit() {
        List<InstProductFeeEntity> productFeeEntities = instProductFeeDao.selectAll(new InstProductFeeEntity());
        productFeeEntities.forEach(item -> {
            List<InstProductFeeEntity> list = CACHE_MAP.computeIfAbsent(item.getInstProductCode(), a -> new ArrayList<>());
            list.add(item);
        });
        CONTRACT_PRODUCT_CACHE_MAP = productFeeEntities.stream().collect(Collectors.groupingBy(c -> Joiner.on("_").join(c.getContractNo(), c.getInstProductCode())));
        CONTRACT_PRODUCT_CAPABILITY_CACHE_MAP = productFeeEntities.stream().collect(Collectors.toMap(c -> Joiner.on("_").join(c.getContractNo(),c.getInstProductCode(),c.getInstProductCapabilityCode()), Function.identity()));
    }

    @Override
    public void doRefresh() {
        Map<String, List<InstProductFeeEntity>> temp = new ConcurrentHashMap<>();
        List<InstProductFeeEntity> productFeeEntities = instProductFeeDao.selectAll(new InstProductFeeEntity());
        productFeeEntities.forEach(item -> {
            List<InstProductFeeEntity> list = temp.computeIfAbsent(item.getInstProductCode(), a -> new ArrayList<>());
            list.add(item);
        });

        Map<String, List<InstProductFeeEntity>> map = productFeeEntities.stream().collect(Collectors.groupingBy(c -> Joiner.on("_").join(c.getContractNo(), c.getInstProductCode())));
        Map<String, InstProductFeeEntity> tmpMap = productFeeEntities.stream().collect(Collectors.toMap(c -> Joiner.on("_").join(c.getContractNo(), c.getInstProductCode(), c.getInstProductCapabilityCode()), Function.identity()));
        CACHE_MAP = temp;
        CONTRACT_PRODUCT_CACHE_MAP = map;
        CONTRACT_PRODUCT_CAPABILITY_CACHE_MAP = tmpMap;
    }

    public List<InstProductFeeEntity> getFeeByProductCode(String productCode) {
        return CACHE_MAP.get(productCode);
    }

    public List<InstProductFeeEntity> getFeeByContractNoAndProductCode(String contractNo,String productCode) {
        return CONTRACT_PRODUCT_CACHE_MAP.get(Joiner.on("_").join(contractNo,productCode));
    }

    public InstProductFeeEntity getFeeByContractNoAndProductCodeAndCapabilityCode(String contractNo,String productCode,String capabilityCode) {
        return CONTRACT_PRODUCT_CAPABILITY_CACHE_MAP.get(Joiner.on("_").join(contractNo,productCode,capabilityCode));
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_PRODUCT_FEE;
    }
}
