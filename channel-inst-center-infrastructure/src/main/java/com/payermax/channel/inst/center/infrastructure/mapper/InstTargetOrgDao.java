package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstTargetOrgEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 12:38
 */
@Mapper
public interface InstTargetOrgDao {
    int insert(InstTargetOrgEntity record);

    int insertBatch(List<InstTargetOrgEntity> records);

    int updateByPrimaryKey(InstTargetOrgEntity record);

    List<InstTargetOrgEntity> selectAll(InstTargetOrgEntity record);

}
