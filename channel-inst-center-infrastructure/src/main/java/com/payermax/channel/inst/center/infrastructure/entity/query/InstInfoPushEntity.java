package com.payermax.channel.inst.center.infrastructure.entity.query;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName InstInfoPushEntity
 * @Description
 * <AUTHOR>
 * @Date 2022/8/17 12:52
 */
@Data
@Builder
@Accessors(chain = true)
public class InstInfoPushEntity {
    /**
     * 机构名称 -> 公司注册名
     * 必填
     */
    private String fullName;

    /**
     * 系统唯一标识 -> 机构编码
     * 必填
     */
    private String thirdId;

    /**
     * 机构性质
     * 1-境内组织
     * 2-境内个人
     * 3-境外组织
     * 4-境外个人
     * 必填
     */
    private String oppositeCharacter;

    /**
     * 机构注册号
     * 机构性质为境外时必填
     */
    private String tinCode;

    /**
     * 机构性质为境外时必填 ->机构主体所在地
     */
    private String countryName;

    /**
     * 法定代表人或负责人
     * 必填
     */
    private String legalPersonName;

    /**
     * 创建人 -> 渠道BD shareid
     * 必填
     */
    private String creatorAccount;

    /**
     * 创建时间
     * 必填，yyyy-MM-dd
     */
    private String creationTime;
}
