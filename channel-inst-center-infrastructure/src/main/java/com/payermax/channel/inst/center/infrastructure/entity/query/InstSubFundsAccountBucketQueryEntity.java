package com.payermax.channel.inst.center.infrastructure.entity.query;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构子级资金账号预申请账号表
 * <AUTHOR>
 * @TableName tb_inst_sub_funds_account_bucket
 */
@Data
public class InstSubFundsAccountBucketQueryEntity {

    /**
     * 机构帐号标识
     */
    private String accountId;

    /**
     * 用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT
     */
    private String subUseType;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 子商户号
     */
    private String subMerchantNo;

    /**
     * 预申请账号状态
     */
    private Integer subAccountStatus;

    /**
     * 状态 Y:可分配，N：已分配
     */
    private String status;
}