package com.payermax.channel.inst.center.infrastructure.mapper;

import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【tb_inst_funds_account_bucket(机构资金账户XX桶表)】的数据库操作Mapper
* @createDate 2022-10-15 18:49:58
* @Entity com.payermax.channel.inst.center.infrastructure.TbInstFundsAccountBucket
*/
public interface InstFundsAccountBucketDao {

    List<InstFundsAccountBucketEntity> selectBucketsIdIsNullByAccountId(InstFundsAccountBucketEntity record);

    List<InstFundsAccountBucketEntity> selectBucketsIdNotNullByBucketsId(InstFundsAccountBucketEntity bucketEntity);

}
