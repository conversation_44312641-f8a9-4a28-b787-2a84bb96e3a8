package com.payermax.channel.inst.center.infrastructure.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.infrastructure.util.RedisUtil;
import com.payermax.common.lang.util.StringUtil;
import com.payermax.infra.tool.lock.redisson.UredissonLock;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * <AUTHOR> at 2022/11/16 15:43
 **/
@Component
@Slf4j
public class RedisClientProxy {

    public static final Long TIME_OUT_MUTEX = 5L;
    public static final TimeUnit TIME_OUT_MUTEX_UNIT = TimeUnit.SECONDS;
    public static final TimeUnit TIMEOUT_UNIT = TimeUnit.SECONDS;
    public static final String LOCK = "lock:";
    @Resource
    private RedisUtil redisClient;
    @Resource
    private UredissonLock uredissonLock;
    @NacosValue(value = "${multilevel-cache.redis.prefix}", autoRefreshed = true)
    private String prefix;
    @NacosValue(value = "${multilevel-cache.redis.prefix.new:instCenterNew:}", autoRefreshed = true)
    private String prefixNew;
    @NacosValue(value = "${multilevel-cache.redis.downgrade:true}", autoRefreshed = true)
    private Boolean downgrade;

    /**
     * 分布式锁获取数据并缓存
     *
     * @param key                Redis键
     * @param function           执行获取数据函数
     * @param timeout            缓存超时时间
     * @return t 返回clazz参数格式
     */
    public <T> List<T> getAndTryAndLock(String key, Supplier<List<T>> function, long timeout, Class<T> t) {
        return this.getAndTryAndLock(key, function, timeout, TIMEOUT_UNIT, t);
    }

    /**
     * 分布式锁获取数据并缓存
     *
     * @param key                Redis键
     * @param function           执行获取数据函数
     * @param timeout            缓存超时时间
     * @return t 返回clazz参数格式
     */
    public <T> List<T> getAndTryAndLock(String key, Supplier<List<T>> function, long timeout, TimeUnit timeoutUnit, Class<T> t) {
        return this.getAndTryAndLock(key, function, timeout, timeoutUnit, TIME_OUT_MUTEX, TIME_OUT_MUTEX_UNIT, t);
    }


    /**
     * 分布式锁获取数据并缓存
     *
     * @param key                Redis键
     * @param function           执行获取数据函数
     * @param timeout            缓存超时时间
     * @param timeoutMutex     缓存失效提前互斥量（时间）
     * @param timeoutMutexUnit 缓存失效提前互斥量（时间单位）
     * @return t 返回clazz参数格式
     */
    public <T> List<T> getAndTryAndLock(String key, Supplier<List<T>> function, long timeout, TimeUnit timeoutUnit, long timeoutMutex, TimeUnit timeoutMutexUnit, Class<T> t) {

        if (StringUtil.isEmpty(key)) {
            return function.get();
        }
        // 适配
        key = prefixNew + key;
        
        List<T> result = null;
        try {
            boolean hasKeys = redisClient.exists(key);

            long expireTimeDifferenceUnit = 0L;
            if (hasKeys) {
                result = redisClient.getList(key, t);
                long expireTime = redisClient.getExpire(key);
                expireTimeDifferenceUnit = timeoutMutexUnit.convert(expireTime, TimeUnit.SECONDS);
            }

            if (!hasKeys || expireTimeDifferenceUnit < timeoutMutex) {
                String lock = prefix + LOCK + key;
                try {
                    boolean locked = uredissonLock.lock(lock, 5, TimeUnit.SECONDS);
                    if (locked) {
                        result = function.get();
                        redisClient.set(key, JSON.toJSONString(result), timeout, timeoutUnit);
                    } else {
                        result = hasKeys ? result : function.get();
                    }
                } catch (Exception e) {
                    log.error("getAndTryAndLock:{}, Get Redis Lock Catch Exception:{}", key, e);
                    throw e;
                } finally {
                    uredissonLock.release(lock);
                }
            }
        } catch (Exception e) {
            log.error("getAndTryAndLock:{}, Handle Redis Catch Exception:{}", key, e);
            // 异常降级处理
            result = this.handleDowngrade(function);
        }
        
        return result;
    }

    /**
     * 缓存查询降级处理
     * 
     * true: 执行业务逻辑查询处理
     * false: 返回null
     */
    public <T> T handleDowngrade(Supplier<T> function) {
        if (downgrade) {
            return function.get();
        }
        return null;
    }

    /**
     * 清理缓存
     */
    public Long clear(String key) {
        try {
            return redisClient.clear(prefixNew + key);
        }catch (Exception e){
            log.error("clear:{}, Redis Catch Exception:{}", key, e);
        }
        return null;
    }
}
