package com.payermax.channel.inst.center.infrastructure.entity;

import lombok.Data;

import java.util.Date;

/**
 * @ClassName ChannelInfoEntity
 * @Description
 * <AUTHOR>
 * @Date 2022/3/23 11:39
 * @Version 1.0
 */
@Data
public class ChannelInfoEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 签约主体 1:SHAREIT_IN  2:FUNTECH  3:PMMAX
     */
    private String entity;

    /**
     * 技术对接机构
     */
    private String technicalOrg;

    /**
     * 资金结算机构
     */
    private String fundsSettleOrg;

    /**
     * 业务处理机构
     */
    private String bizHandleOrg;

    /**
     * 渠道类型 I:入款 O:出款 T:资金调拨 F:外汇 R:风控合规
     */
    private String channelType;

    /**
     * 交互模式 0:正常 1:异步 2:人工 3:虚拟 4:线下
     */
    private Byte interactiveMode;

    /**
     * 是否需要报备商户 1：是 0：否
     */
    private Byte isNeedReportMerchant;

    /**
     * 交易码映射配置组
     */
    private String transCodeMapGroupId;

    /**
     * 响应码映射配置组
     */
    private String responseCodeMapGroupId;

    /**
     * 扩展配置
     */
    private String configJson;

    /**
     * 状态 1：可用 0：不可用
     */
    private Byte status;

    /**
     * 业务处理机构号
     */
    private String bizHandleInstCode;

    /**
     * 资金结算机构号
     */
    private String fundsSettleInstCode;

    /**
     * 最后更新时间
     */
    private Date modifiedTime;
}
