package com.payermax.channel.inst.center.infrastructure.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.model.contract.ContractAiParse;
import com.payermax.common.lang.util.AssertUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DifyHttpAdapter {


    @NacosValue(value = "${dify.http.workflow.url:https://dify.payermax.com/v1/workflows/run}", autoRefreshed = true)
    private String DIFY_WORKFLOW_URL;

    @NacosValue(value = "${dify.http.workflow.timeout.seconds:600}", autoRefreshed = true)
    private int TIMEOUT_SECONDS;

    @NacosValue(value = "#{${dify.http.workflow.api-key.map}}", autoRefreshed = true)
    private Map<String,String> DIFY_WORKFLOW_API_KEY_MAP;

    private static final String USER_ID = "INST_CONTRACT_ADMIN";



    /**
     * 合约内容简化
     */
    public ContractAiParse.ContentSimplify contractContentSimplify(String fileUrl){
        log.info("调用 DIFY 工作流简化合约内容: {}", fileUrl);
        Map<String,String> contractFileJson = new HashMap<String, String>(4){{
            put("type", "document");
            put("transfer_method", "remote_url");
            put("url", fileUrl);
        }};
        Map<String, Object> inputs = new HashMap<String, Object>(4){{
            put("contractFile", contractFileJson);
        }};

        Map<String, Object> result = workflowBlockCaller(inputs, DIFY_WORKFLOW_API_KEY_MAP.get("contractContentSimplify"), USER_ID);
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(result), ErrorCodeEnum.INST_CENTER_CONTRACT_AI_PARSE_ERROR.getCode(), "Dify 工作流简化合约内容失败");
        return JSON.parseObject(JSON.toJSONBytes(result), ContractAiParse.ContentSimplify.class);
    }


    /**
     * 提取合约币种
     */
    public ContractAiParse.CurrencyExtract contractCurrencyExtract(String content){
        log.info("调用 DIFY 工作流提取合约币种");
        Map<String, Object> inputs = new HashMap<String, Object>(4){{
            put("contractContent", content);
        }};
        Map<String, Object> result = workflowBlockCaller(inputs, DIFY_WORKFLOW_API_KEY_MAP.get("contractCurrencyExtract"), USER_ID);
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(result), ErrorCodeEnum.INST_CENTER_CONTRACT_AI_PARSE_ERROR.getCode(), "Dify 工作流提取合约币种失败");
        return JSON.parseObject(JSON.toJSONBytes(result), ContractAiParse.CurrencyExtract.class);
    }

    /**
     * 转换币种费用
     */
    public List<ContractAiParse.FeeItem> currencyFeeParse(String content, String ccy) {
        log.info("调用 DIFY 工作流转换币种费用");
        Map<String, Object> inputs = new HashMap<String, Object>(4){{
            put("contractContent", content);
            put("currency", ccy);
        }};
        Map<String, Object> result = workflowBlockCaller(inputs, DIFY_WORKFLOW_API_KEY_MAP.get("contractCurrencyFeeParse"), USER_ID);
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(result) && result.containsKey("fees")
                , ErrorCodeEnum.INST_CENTER_CONTRACT_AI_PARSE_ERROR.getCode(), "Dify 工作流转换币种费用失败");

        return JSON.parseArray(JSON.toJSONString(result.get("fees")), ContractAiParse.FeeItem.class);
    }


    /**
     * 发起 Dify 工作流调用
     */
    private Map<String, Object> workflowBlockCaller(Map<String, Object> inputs, String apiKey, String userId) {
        long startTime = System.currentTimeMillis();

        OkHttpClient client = createHttpClient();
        Request request = buildRequest(inputs, apiKey, userId);

        log.info("Dify 工作流请求参数: {}", JSON.toJSONString(inputs));

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String responseBody = response.body() != null ? response.body().string() : "无响应体";
                log.error("请求失败，状态码: {}, 响应内容: {}", response.code(), responseBody);
                return null;
            }

            String responseBody = response.body().string();
            Map<String, Object> outputs = parseResponse(responseBody);

            // 计算调用耗时
            long endTime = System.currentTimeMillis();
            log.info("调用耗时: {} 秒", (endTime - startTime) / 1000.0);
            return outputs;

        } catch (IOException e) {
            log.error("请求异常: {}, 请求参数: {}", e.getMessage(), JSON.toJSONString(inputs), e);
            return null;
        }
    }

    /**
     * 创建 OkHttpClient 实例
     */
    private OkHttpClient createHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 构建 HTTP 请求
     */
    private Request buildRequest(Map<String, Object> inputs, String apiKey, String userId) {
        JSONObject payload = new JSONObject();
        payload.put("inputs", inputs);
        payload.put("response_mode", "blocking");
        payload.put("user", userId);

        MediaType mediaType = MediaType.Companion.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.Companion.create(JSON.toJSONString(payload), mediaType);

        return new Request.Builder()
                .url(DIFY_WORKFLOW_URL)
                .header("Authorization", "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .post(requestBody)
                .build();
    }

    /**
     * 解析响应数据
     */
    private Map<String, Object> parseResponse(String responseBody) {
        JSONObject responseJson = JSONObject.parseObject(responseBody);
        JSONObject data = responseJson.getJSONObject("data");
        return JSON.parseObject(String.valueOf(data.get("outputs")), new TypeReference<Map<String, Object>>() {});
    }


}
