package com.payermax.channel.inst.center.infrastructure.cache.item;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payermax.channel.inst.center.infrastructure.cache.AbstractLocalCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractOriginProductMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> tracy
 * @version 2023-08-13 3:22 PM
 */
@Service
public class InstNewContractOriginProdCacheManager extends AbstractLocalCacheManager {

    @Resource
    private InstContractOriginProductMapper instContractOriginProductMapper;

    private static Map<String, List<InstContractOriginProductPO>> CACHE_MAP = new HashMap<>();


    public List<InstContractOriginProductPO> queryOriginProductByContract(String contractNo, String contractVersion) {

        return CACHE_MAP.get(contractNo + CACHE_CONNECT + contractVersion);
    }


    @Override
    public void doInit() {
        QueryWrapper<InstContractOriginProductPO> queryWrapper = new QueryWrapper<>();
        List<InstContractOriginProductPO> instContractOriginProductPOList = instContractOriginProductMapper.selectList(queryWrapper);
        instContractOriginProductPOList.forEach(item -> {
            List<InstContractOriginProductPO> standardProductPOList = CACHE_MAP
                    .computeIfAbsent(item.getContractNo() + CACHE_CONNECT + item.getContractVersion(), a -> new ArrayList<>());
            standardProductPOList.add(item);
        });
    }

    @Override
    public void doRefresh() {
        Map<String, List<InstContractOriginProductPO>> temp = new HashMap<>();

        QueryWrapper<InstContractOriginProductPO> queryWrapper = new QueryWrapper<>();
        List<InstContractOriginProductPO> instContractOriginProductPOList = instContractOriginProductMapper.selectList(queryWrapper);
        instContractOriginProductPOList.forEach(item -> {
            List<InstContractOriginProductPO> standardProductPOList = temp
                    .computeIfAbsent(item.getContractNo() + CACHE_CONNECT + item.getContractVersion(), a -> new ArrayList<>());
            standardProductPOList.add(item);
        });
        CACHE_MAP = temp;
    }

    @Override
    public CacheEnum getCacheName() {
        return CacheEnum.INST_NEW_CONTRACT_ORIGIN_PROD;
    }
}
