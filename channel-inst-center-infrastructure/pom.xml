<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.payermax.channel</groupId>
        <artifactId>channel-inst-center-parent</artifactId>
        <version>1.0.0-RELEASE</version>
    </parent>

    <artifactId>channel-inst-center-infrastructure</artifactId>
    <packaging>jar</packaging>
    <name>channel-inst-center-infrastructure</name>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-inst-center-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-inst-center-domain</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.payermax.common</groupId>-->
<!--            <artifactId>fintech-components-log-dubbo3</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-log-digest-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-log-digest-dubbo3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.payermax.common</groupId>
            <artifactId>common-enum</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ushareit.fintech.base</groupId>
            <artifactId>fintech-base-manage-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>operating-log-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ushareit.fintech.parent</groupId>
            <artifactId>fintech-components-alarm-dingtalk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ushareit.pay.member</groupId>
            <artifactId>pay-member-http-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-tool-distributed-id</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>software.aws.rds</groupId>
            <artifactId>aws-mysql-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-tool-distributed-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating.crm</groupId>
            <artifactId>omc-crm-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.file</groupId>
            <artifactId>fin-file-exchange-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax</groupId>
            <artifactId>channel-gateway-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax</groupId>
            <artifactId>omc-channel-exchange-facade</artifactId>
        </dependency>
        <!-- 文件存储 AWS S3、华为与OBS集合体 -->
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-fs-all-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>operating-workflow-dubbo-api</artifactId>
            <version>1.2.2-20230606-RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-tool-notice</artifactId>
        </dependency>
        <!-- 事件沟通中心-消息通知  -->
        <dependency>
            <groupId>com.payermax.solution</groupId>
            <artifactId>commcenter-notice-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-config-encrypt-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
    </dependencies>
</project>
