package com.payermax.channel.inst.center.facade.service.impl;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/14
 * @DESC
 */
public class SettleDateUtils {


    /**
     * 判断传入时间是否为节假日
     */
    public static boolean isHoliday(Calendar calendar) {
        return calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY
                || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY;
    }

    /**
     * 日期推进，忽略周末
     */
    public static Calendar addDaysSkipWeekends(long nowDate, int days, boolean onMonthStart) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(nowDate));
        int addedDays = 0;

        // 是否需要从当月第一天开始计算
        if(onMonthStart){
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            addedDays = 1;
        }

        while (addedDays < days) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            if (!SettleDateUtils.isHoliday(calendar)) {
                ++addedDays;
            }
        }

        return calendar;
    }


    /**
     * 当天是周末时，推进直到工作日
     */
    public static Date skipWhenWeekends(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        while (SettleDateUtils.isHoliday(calendar)){
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        return calendar.getTime();
    }

    /**
     * 当天是周末时，回退直到工作日
     */
    public static Date pushbackWhenWeekends(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        while (SettleDateUtils.isHoliday(calendar)){
            calendar.add(Calendar.DAY_OF_MONTH, -1);
        }
        return calendar.getTime();
    }
}
