package com.payermax.channel.inst.center.facade.request.contract;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/11/4
 * @DESC 风控费用请求参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstRiskFeeQueryRequest extends InstInfoQueryRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 风控产品大类-cbAlert
     */
    @NotBlank(message = "风控产品大类不能为空")
    private String riskProductType;

    /**
     * 风控产品具体类型，支持通配
     */
    @NotBlank(message = "风控产品具体类型不能为空")
    private String riskProductSpecificType;

    /**
     * 预警币种
     */
    @NotBlank(message = "预警币种不能为空")
    private String riskTradeCurrency;

    /**
     * mcc
     */
    private String mcc;
}
