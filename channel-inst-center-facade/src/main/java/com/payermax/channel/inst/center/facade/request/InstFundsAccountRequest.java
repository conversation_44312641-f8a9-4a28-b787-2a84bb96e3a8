package com.payermax.channel.inst.center.facade.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 新版资金账号详细信息
 */
@Data
public class InstFundsAccountRequest implements Serializable {

    private static final long serialVersionUID = 1313885732726674467L;

    /**
     * 机构帐号标识（机构标识_机构账号_国家_币种）
     */
    private String accountId;

    /**
     * 机构标识
     */
    private String instCode;

    /**
     * 签约主体
     */
    private String entity;

    /**
     * 机构账号
     */
    private String accountNo;

    /**
     * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
     */
    private String accountType;

    /**
     * 机构账号用途：充值：RECHARGE、收款：RECEIVE_PAY、结算：SETTLEMENT、充退：CHARGE_BACK、代发：ISSUED_ON_BEHALF、保证金：SECURITY_DEPOSIT,支持多个使用,分割
     */
    private String useType;

    /**
     * 业务用途 I 代收/O 代付
     */
    private String bizType;

    /**
     * 国家
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    //----2023-04 新增的---//
    /**
     * 账户别名
     */
    private String accountAlias;

}
