package com.payermax.channel.inst.center.facade.request.contract;

import com.payermax.channel.inst.center.facade.enums.contract.ContractBizTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/20
 * @DESC
 */
@Data
public class InstContractFxBatchSyncRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 加点变更列表
     */
    List<FxConfigItem> fxConfigSyncList;

    /**
     * 加点变更项
     */
    @Data
    public static class FxConfigItem implements Serializable{

        private static final long serialVersionUID = 1L;

        /**
         * 业务大类 - 入款(I) OR 出款(O) OR 风控(R) OR 技术服务费(TS) OR VA(VA)
         * <p>
         * {@link ContractBizTypeEnum}
         */
        private String bizType;

        /**
         * 机构编码
         */
        private String instCode;

        /**
         * 币种
         */
        private String payCurrency;

        /**
         * 签约主体
         */
        private String entity;

        /**
         * 合约外汇加点
         */
        private String contractFxSpread;

        /**
         * 外汇加点
         */
        private String fxSpread;

        /**
         * 换汇时机
         * <p>
         * {@link  com.payermax.channel.inst.center.facade.request.contract.config.content.CurrencyExchangeTiming}
         */
        private String currencyExchangeTime;
    }

}
