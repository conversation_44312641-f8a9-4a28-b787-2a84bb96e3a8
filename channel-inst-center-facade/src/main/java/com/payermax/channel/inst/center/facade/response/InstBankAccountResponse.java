package com.payermax.channel.inst.center.facade.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2023/8/10  3:11 PM
 */
@Data
public class InstBankAccountResponse extends BaseResponse {

    private static final long serialVersionUID = -3854917236358772249L;

    /**
     * 机构标识
     */
    private String instCode;

    private String country;

    private String currency;

    private String bankCode;

    private String bankName;

    private String branch;

    private String branchAddress;

    /**
     * 开户名
     */
    private String accountName;

    /**
     * 账户
     */
    private String accountNo;

    /**
     * swiftCode
     */
    private String swiftCode;

    /**
     * iban
     */
    private String iban;

    /**
     * 我方渠道充值场景下，可充值的币种列表
     */
    private List<String> rechargeCanUseCcyList;

    /**
     * 我方渠道充值场景下，当前账户的优先级
     */
    private Integer rechargeUseOrder;

    /**
     * 账户用途，见 UseTypeEnum
     */
    private List<String> accountUseList;
}
