package com.payermax.channel.inst.center.facade.api;

import com.payermax.channel.inst.center.facade.request.contract.InstContractFxBatchSyncRequest;
import com.payermax.channel.inst.center.facade.request.contract.config.mapping.InstChannelMerchantCodeRequest;
import com.payermax.common.lang.model.dto.Result;

/**
 * 新版机构中心信息管理接口
 *
 * <AUTHOR> tracy
 * @version 2023-08-11 5:23 PM
 */
public interface InstNewInfoManagerFacade {

    /**
     * 填充机构给我们开的MID
     */
    Result<String> fillInstChannelMerchantCode(InstChannelMerchantCodeRequest request);

    /**
     * 机构外汇加点批量同步
     */
    Result<Boolean> instContractFxBatchSync(InstContractFxBatchSyncRequest request);
}
