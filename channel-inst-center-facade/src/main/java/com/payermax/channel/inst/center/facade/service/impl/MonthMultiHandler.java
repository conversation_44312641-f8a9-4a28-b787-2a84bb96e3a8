package com.payermax.channel.inst.center.facade.service.impl;

import com.payermax.channel.inst.center.facade.service.CycleUnitHandler;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR> t<PERSON>
 * @version 2024/1/25 15:05
 */
public class MonthMultiHandler extends Month<PERSON>ycleUnitHandler implements CycleUnitHandler {

    @Override
    public long calculateCycleTime(SettleInfoVo settleInfoVo, long transactionTime) {
        String otherLimit = settleInfoVo.getSettleCycleOtherLimit();
        String[] days = otherLimit.split(",");

        //防止最后一个是"-1"之类
        if (Integer.parseInt(days[days.length - 1]) < 0) {
            Date date = getDayOfNMonth(transactionTime, days[days.length - 1], 0, false);
            days[days.length - 1] = String.valueOf(getCurrDayOfMonth(date.getTime()));
        }

        int dayOfMonth = getCurrDayOfMonth(transactionTime);

        int monthDay = getFinalMonthDay(dayOfMonth, days);
        if (monthDay >= dayOfMonth) {
            return SettleDateUtils.skipWhenWeekends(getDayOfNMonth(transactionTime, String.valueOf(monthDay), 0, false)).getTime();
        } else {
            return SettleDateUtils.skipWhenWeekends(getDayOfNMonth(transactionTime, String.valueOf(monthDay), 1, false)).getTime();
        }
    }

    private int getFinalMonthDay(int dayOfWeek, String[] days) {
        int rangeStart = Integer.parseInt(days[days.length - 1]);
        for (String kk : days) {
            int round = Integer.parseInt(kk);
            if (dayOfWeek >= rangeStart && dayOfWeek < round) {
                return round;
            } else {
                rangeStart = round;
            }
        }
        //代表是最后一个到第一个的区间
        return Integer.parseInt(days[0]);
    }

    private int getCurrDayOfMonth(long transactionTime) {
        Calendar date = Calendar.getInstance();
        date.setTime(new Date(transactionTime));
        return date.get(Calendar.DAY_OF_MONTH);
    }

}
