spring.application.name=channel-inst-center
spring.main.banner-mode=off
spring.main.allow-bean-definition-overriding=true
spring.jackson.default-property-inclusion=non_null
server.port=9021
server.servlet.context-path=/channel-inst-center
server.compression.enabled=true
server.compression.mime-types=application/json,text/html

# nacos 全局配置
nacos.config.server-addr=${nacos.config.server-addr}
nacos.config.dataIds=application.properties
nacos.config.type=properties
nacos.config.group=${spring.application.name}
nacos.config.autoRefresh=true
nacos.config.remoteFirst=true
nacos.config.bootstrap.enable=true
nacos.config.bootstrap.logEnable=false
nacos.config.username=${nacos.config.username}
nacos.config.password=${nacos.config.password}
nacos.config.namespace=${nacos.config.namespace}
nacos.config.enable-remote-sync-config=true

# mybatis-plus 全局配置，定制化处理
mybatis-plus.typeEnumsPackage=com.payermax.channel.inst.center.enums
mybatis-plus.mapper-locations=classpath*:mapper/**/*.xml
mybatis-plus.configuration.default-enum-type-handler=org.apache.ibatis.type.EnumTypeHandler
mybatis-plus.type-aliases-package=com.payermax.channel.inst.center.infrastructure.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# dubbo指标统一监控
management.endpoints.web.exposure.include=health,info,prometheus
management.endpoints.web.base-path=/
management.endpoints.web.path-mapping.prometheus=/metrics
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}
management.server.port=10108

# sentry错误日志监控 参考文档：https://shimo.im/docs/dPkpKyZlZyHgEmqO
sentry.traces-sample-rate=0.2

# 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
# 不要设置过大，如果过大，启动项目会报错：打开文件数过多
server.undertow.threads.io=16
# 阻塞任务线程池, 当执行类似servlet请求阻塞IO操作, undertow会从这个线程池中取得线程
# 它的值设置取决于系统线程执行任务的阻塞系数，默认值是IO线程数*8
server.undertow.threads.worker=256
# 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
# 每块buffer的空间大小,越小的空间被利用越充分，不要设置太大，以免影响其他应用，合适即可
server.undertow.buffer-size=1024
server.undertow.url-charset=UTF-8
# 是否分配的直接内存(NIO直接分配的堆外内存)
server.undertow.direct-buffers=true

# 钉钉告警机器人 线下token（线上会另行覆盖）
dingTalk.robot.accessToken=2f41767b6e9ed64663aea8a470e39471151b092c34135b512e0fb202e48713f6

#业务配置
effect.delay.seconds=5

#数据源迁移改造配置
database.migrate.pay.start=false