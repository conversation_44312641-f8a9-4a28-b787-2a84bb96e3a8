package com.payermax.channel.inst.center.domain.entity.businessDraft;

import com.baomidou.mybatisplus.annotation.TableField;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @DESC
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstBusinessDraft implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 草稿id，唯一主键
     */
    private String draftId;

    /**
     * 草稿数据
     */
    private String draftData;

    /**
     * 业务类型
     */
    private BusinessTypeEnum businessType;

    /**
     * 模块名称
     */
    private OperateModuleEnum moduleName;

    /**
     * 行为名称
     */
    private OperateTypeEnum operateType;

    /**
     * 所属编辑者
     */
    private String owner;

    /**
     * 当前关联流程单号
     */
    private String relatedProcessId;

    /**
     * 业务key
     */
    private String businessKey;

    /**
     * 扩展字段
     */
    private String extendFields;

    /**
     * 状态
     */
    private InstProcessStatusEnum status;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 创建时间
     */
    private LocalDateTime utcCreate;

    /**
     * 更新时间
     */
    private LocalDateTime utcModified;

    /**
     * 开始时间
     */
    @TableField(exist = false)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField(exist = false)
    private LocalDateTime endTime;


    /**
     * 草稿是否到达终态
     */
    public boolean isFinalStatus() {
        return InstProcessStatusEnum.PASS.equals(status) || InstProcessStatusEnum.REJECT.equals(status) || InstProcessStatusEnum.REVOKE.equals(status) || InstProcessStatusEnum.STOP.equals(status);
    }
}
