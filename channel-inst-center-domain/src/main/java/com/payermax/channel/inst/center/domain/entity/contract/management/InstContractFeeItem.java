package com.payermax.channel.inst.center.domain.entity.contract.management;

import com.payermax.channel.inst.center.common.enums.LogicKeyEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.CardTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.ClearNetworkEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.CustomerTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.FeeBearerEnum;
import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstTaxConfig;
import com.payermax.channel.inst.center.domain.enums.contract.content.CurrencyExchangeTimingEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.RoundingModeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> at 2023/6/9 10:15 AM
 **/
@Data
public class InstContractFeeItem implements IInstContractItemRecorder, IInstContractItemValidate {

    /**
     * 合同费用条款项id
     */
    private String instContractFeeItemNo;

    /**
     * 机构原始产品编码
     */
    private String instOriginProductNo;

    /**
     * 支付币种
     */
    private String payCurrency;


    /**
     * 机构原始mid，及标准化后的内部渠道商户号
     */
    private String originMid;
    private String channelMerchantNo;

    private String subMerchantNo;


    /**
     * 机构合同中行业标识，及标准化后的内部统一mcc
     */
    private LogicKeyEnum mccLogic;
    private String originMcc;
    private String standardMcc;

    /**
     * 资金源
     */
    private String fundingSource;

    /**
     * 清算网络
     * {@link ClearNetworkEnum}
     */
    private String clearNetwork;

    /**
     * 费用承担方
     * {@link FeeBearerEnum}
     */
    private String feeBearer;

    /**
     * 客户类型
     * {@link CustomerTypeEnum}
     */
    private String customerType;

    /**
     * 卡类型
     * {@link CardTypeEnum}
     */
    private String cardType;

    /**
     * 税费配置
     */
    private Map<FeeTypeEnum, InstFeeConfig> feeConfigs = new HashMap<>();

    private List<InstTaxConfig> taxConfigs = new ArrayList<>();


    /**
     * 合约外汇加点
     */
    private BigDecimal contractFxSpread;

    /**
     * 外汇加点
     */
    private BigDecimal fxSpread;

    private CurrencyExchangeTimingEnum currencyExchangeTime;


    private Integer roundingScale;

    private RoundingModeEnum roundingMode;


    //-- 出款项目新增 --//
    /**
     * 发卡国家新增
     */
    private String cardIssueCountry;

    /**
     * 交易国家新增
     */
    private String transactionCountry;

    //----- 大阶梯相关
    private AccumulationCycle accumulationCycle;

    private AccumulationType accumulationType;

    private AccumulationMethod accumulationMethod;

    private AccumulationRange accumulationRange;

    private AccumulationDeductTime accumulationDeductTime;

    private String accumulationJoin;

    private String accumulationKey;

    private String accumulationMode;
    //----- 大阶梯相关

    /**
     * 业务唯一性约束
     */
    private String feeBusinessKey;

    /**
     * 业务校验
     */
    @Override
    public List<ExportErrorInfo.ExportErrorInfoItem> businessValidateOnContractItem() {
        List<ExportErrorInfo.ExportErrorInfoItem> errorInfoItemList = new ArrayList<>();

        // 手续费 校验
        feeConfigs.values().forEach(item -> {
            // 自身合法性校验
            errorInfoItemList.addAll(item.businessValidateOnContractItem());

            // 币种校验，和payCurrency一致 或者 等于USD
            errorInfoItemList.addAll(item.businessValidateOnCurrency(payCurrency));
        });

        // 税费 自身业务合法性校验
        taxConfigs.stream().forEach(item -> errorInfoItemList.addAll(item.businessValidateOnContractItem()));

        return errorInfoItemList;
    }
}
