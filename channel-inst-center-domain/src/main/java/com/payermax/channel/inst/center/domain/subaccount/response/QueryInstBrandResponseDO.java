package com.payermax.channel.inst.center.domain.subaccount.response;

import lombok.Data;

/**
 * QueryInstBrandResponseDO
 *
 * <AUTHOR>
 * @desc
 */
@Data
public class QueryInstBrandResponseDO {

    private Long brandId;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * bd shareId
     */
    private String bdId;

    /**
     * bd姓名
     */
    private String bdName;

    /**
     * 状态，Y：可用、N：不可用
     */
    private String status;
}
