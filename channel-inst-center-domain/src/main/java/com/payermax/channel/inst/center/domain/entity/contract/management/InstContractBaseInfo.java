package com.payermax.channel.inst.center.domain.entity.contract.management;

import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> at 2023/6/8 7:42 AM
 *
 * 主合同信息，作为机构合同入口类，主要维护合同不随版本变更的信息
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstContractBaseInfo {

    private String contractNo;

    private String instCode;

    private String contractEntity;

    private ContractBusinessTypeEnum instProductType;

    private ContractStatusEnum status;

    /**
     * 是否新创建的，辅助数据库操作
     */
    private boolean newlyCreated;
}
