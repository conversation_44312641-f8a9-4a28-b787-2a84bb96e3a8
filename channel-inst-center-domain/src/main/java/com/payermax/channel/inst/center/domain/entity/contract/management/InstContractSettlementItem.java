package com.payermax.channel.inst.center.domain.entity.contract.management;

import com.payermax.channel.inst.center.common.enums.LogicKeyEnum;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettleDateConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettlePaymentConfig;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> at 2023/6/9 10:16 AM
 **/
@Data
public class InstContractSettlementItem implements IInstContractItemRecorder {

    /**
     * 合同结算条款项id
     */
    private String instContractSettlementItemNo;

    /**
     * 机构原始产品编码
     */
    private String instOriginProductNo;

    /**
     * 支付币种
     */
    private String payCurrency;


    /**
     * 机构原始mid，及标准化后的内部渠道商户号
     */
    private String originMid;
    private String channelMerchantNo;


    /**
     * 机构合同中行业标识，及标准化后的内部统一mcc
     */
    private LogicKeyEnum mccLogic;
    private String originMcc;
    private String standardMcc;


    private String                      settleCurrency;

    /**
     * 结算费用
     */
    private InstFeeConfig               settleFeeConfig;

    /**
     * 结算周期
     */
    private List<InstSettleDateConfig>  settleDateConfigs;

    /**
     * 提现打款
     */
    private InstSettlePaymentConfig     settlePaymentConfig;

}
