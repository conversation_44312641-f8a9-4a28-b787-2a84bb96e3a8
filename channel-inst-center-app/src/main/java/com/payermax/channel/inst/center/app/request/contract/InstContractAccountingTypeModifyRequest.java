package com.payermax.channel.inst.center.app.request.contract;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/1/10
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstContractAccountingTypeModifyRequest extends InstContractBaseRequest {


    /**
     * 合同号
     */
    private String contractNo;

    /**
     * 合同版本编号
     */
    private String  contractVersion;

    /**
     * 渠道核对方式
     */
    private JSONObject accountingTypeJson;
}
