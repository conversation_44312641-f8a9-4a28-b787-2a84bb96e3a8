package com.payermax.channel.inst.center.app.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName InstAccountQueryReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/7/12 13:35
 */
@Data
public class InstAccountQueryReqDTO implements Serializable {
    private static final long serialVersionUID = 4124287673003536017L;

    @ApiModelProperty(notes = "申请单号")
    private String applyNo;

    @ApiModelProperty(notes = "机构标识")
    private Long instId;

    @ApiModelProperty(notes = "集成需求单ID")
    private Long requirementOrderId;

    @ApiModelProperty(notes = "平台地址")
    private String prodPlatformUrl;

    @ApiModelProperty(notes = "产线要素")
    private List<InstAccountReqDTO> instAccountReqDTOS;
}
