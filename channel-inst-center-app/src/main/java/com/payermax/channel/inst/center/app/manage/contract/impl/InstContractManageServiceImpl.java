package com.payermax.channel.inst.center.app.manage.contract.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Maps;
import com.payermax.channel.inst.center.app.assembler.contract.InstContractInfoAssembler;
import com.payermax.channel.inst.center.app.assembler.po.ContractVersionPOAssembler;
import com.payermax.channel.inst.center.app.dto.contract.InstContractVersionInfoDto;
import com.payermax.channel.inst.center.app.factory.OperateLogFactory;
import com.payermax.channel.inst.center.app.manage.contract.InstContractManageService;
import com.payermax.channel.inst.center.app.manage.contract.processHandler.InstContractVersionUpgradeHandler;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.request.contract.InstContractAccountingTypeModifyRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractAiParseQueryRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractInfoQueryRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractVersionUpgradeRequest;
import com.payermax.channel.inst.center.app.response.InstContractAiParseQueryResponse;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.InstProductTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogOperateResTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractBaseInfo;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.adapter.FileDownloadService;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.cache.CacheRegistry;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOperateLogPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOperateLogRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractVersionInfoRepository;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.response.RowResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/9
 * @DESC
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class InstContractManageServiceImpl implements InstContractManageService {

    private final InstContractBaseInfoRepository baseInfoRepository;
    private final ContractVersionPOAssembler contractVersionPOAssembler;
    private final InstContractInfoAssembler contractInfoAssembler;
    private final InstContractVersionInfoRepository versionInfoRepository;
    private final OperateLogFactory operateLogFactory;
    private final InstContractOperateLogRepository operateLogRepository;
    private final FileDownloadService fileDownloadService;
    private final InstContractVersionUpgradeHandler contractVersionUpgradeHandler;
    private final InstContractAiParseHandler aiParseHandler;
    private final InstBusinessDraftRepository businessDraftRepository;


    /**
     * 合约模板地址
     */
    @NacosValue(value = "#{${inst.contract.template.url}}", autoRefreshed = true)
    private HashMap<String,String> contractTemplateUrlMap = new HashMap<>();


    @Override
    public RowResponse<InstContractVersionInfoDto> queryInstContractInfo(InstContractInfoQueryRequest request) {
        // 查询合同列表
        InstContractBaseInfoPO po = contractInfoAssembler.request2Po(request);
        RowResponse<InstContractBaseInfo> baseInfoRow = contractInfoAssembler.poRow2Domain(baseInfoRepository.queryActiveBaseInfoPageable(po, request.getPageNum(), request.getPageSize()));

        // 查询合同版本
        List<String> contractNoList = baseInfoRow.getRows().stream().map(InstContractBaseInfo::getContractNo).collect(Collectors.toList());
        Map<String, List<InstContractVersionInfo>> versionGroup = contractVersionPOAssembler
                .convertVersionDomainList(versionInfoRepository.queryActiveVersionByContracts(contractNoList))
                .stream().collect(Collectors.groupingBy(InstContractVersionInfo::getContractNo));

        // 填充版本信息
        List<InstContractVersionInfoDto> contractList = baseInfoRow.getRows().stream()
                .filter(contract -> versionGroup.containsKey(contract.getContractNo()))
                .filter( contract -> CollectionUtils.isNotEmpty(versionGroup.get(contract.getContractNo())))
                .map(contract -> {
                    InstContractVersionInfo latestVersion = versionGroup.get(contract.getContractNo()).stream()
                            .max(Comparator.comparing(InstContractVersionInfo::getEffectStartTime)).orElseGet(InstContractVersionInfo::new);
                    return contractInfoAssembler.contractAndVersion2Dto(contract, latestVersion);
                })
                .collect(Collectors.toList());

        return contractInfoAssembler.buildContractVersionInfoDtoRowResponse(baseInfoRow.getTotal(), contractList);
    }

    @Override
    public Boolean updateAccountingType(InstContractAccountingTypeModifyRequest request, String operator) {
        InstContractVersionInfo originVersion = contractVersionPOAssembler.convertVersionInfoDomain(versionInfoRepository.queryByPrimaryKey(contractInfoAssembler.accountingTypeModifyReq2Po(request)));
        AssertUtil.isTrue(Objects.nonNull(originVersion), ErrorCodeEnum.INST_CONTRACT_ACCOUNTING_TYPE_MODIFY_ERROR.getCode(), "合同版本不存在");

        InstContractVersionInfo modifiedVersion = contractInfoAssembler.deepCopy(originVersion);
        Map<String, Object> filterAccountingType = Maps.filterValues(request.getAccountingTypeJson(), value -> StringUtils.isNotBlank(String.valueOf(value)));
        modifiedVersion.setAccountingType(JSON.toJSONString(filterAccountingType));
        versionInfoRepository.updateByPrimaryKey(contractVersionPOAssembler.convertVersionInfoPO(modifiedVersion));

        InstContractOperateLogPO log = operateLogFactory.composeOperateLog(operator, modifiedVersion.getContractVersion(), originVersion, modifiedVersion, OperateModuleEnum.INST_CENTER_CONTRACT_MANAGER, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.UPDATE);
        operateLogRepository.saveWithoutBlocking(Collections.singletonList(log));
        return Boolean.TRUE;
    }

    @Override
    public Boolean contractVersionUpgrade(InstContractVersionUpgradeRequest request, String operator) {
        return contractVersionUpgradeHandler.startUpgrade(request, operator);
    }

    @Override
    public String contractTemplateDownload(String contractBizType) {
        log.info("bizType:{}, contractTemplateUrlMap:{}", contractBizType, contractTemplateUrlMap);
        InstProductTypeEnum productType = Arrays.stream(InstProductTypeEnum.values())
                .filter(item -> item.getValue().equalsIgnoreCase(contractBizType))
                .findFirst()
                .orElseThrow(() -> new BusinessException(ErrorCodeEnum.INST_CENTER_CONTRACT_TEMPLATE_DOWNLOAD_ERR.getCode(), "合约类型错误"));
        AssertUtil.isTrue(contractTemplateUrlMap.containsKey(productType.getBusinessName()), ErrorCodeEnum.INST_CENTER_CONTRACT_TEMPLATE_DOWNLOAD_ERR.getCode(), "合约模板不存在");
        String filePath = contractTemplateUrlMap.get(productType.getBusinessName());
        return fileDownloadService.getCdnUrlByFilename(filePath);
    }

    @Override
    public Boolean instContractCacheRefresh(){
        try{
            CacheRegistry.getCacheManager(CacheEnum.INST_NEW_CONTRACT_MID_MAPPING).refresh();
            CacheRegistry.getCacheManager(CacheEnum.INST_NEW_CONTRACT_STANDARD_PROD).refresh();
            CacheRegistry.getCacheManager(CacheEnum.INST_NEW_CONTRACT_ORIGIN_PROD).refresh();
            CacheRegistry.getCacheManager(CacheEnum.INST_NEW_CONTRACT_FEE).refresh();
            CacheRegistry.getCacheManager(CacheEnum.INST_NEW_CONTRACT_SETTLE).refresh();
            CacheRegistry.getCacheManager(CacheEnum.INST_NEW_CONTRACT_BASE_INFO).refresh();
            CacheRegistry.getCacheManager(CacheEnum.INST_NEW_CONTRACT).refresh();
        } catch (Exception e) {
            log.error("机构合约缓存刷新失败");
        }

        return Boolean.TRUE;
    }

    @Override
    public List<InstContractAiParseQueryResponse> getAiParseList(InstContractAiParseQueryRequest request) {
        // 查询所有草稿
        businessDraftRepository.queryByConditions()

        return Collections.emptyList();
    }

    @Override
    public String contractAiParse(InstContractParseRequestDTO request) {
        return aiParseHandler.acceptAiParse(request);
    }

}
