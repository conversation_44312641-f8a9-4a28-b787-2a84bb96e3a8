package com.payermax.channel.inst.center.app.assembler.domain;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBusinessDraftPO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/9
 * @DESC
 */
@Mapper(componentModel = "spring",nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {JSON.class, Optional.class, FeeConfig.class, TaxConfig.class, StringUtils.class, BigDecimal.class, LocalDate.class,
                Objects.class, InstProcessStatusEnum.class, BusinessTypeEnum.class})
public interface InstBusinessDraftAssembler {



    InstBusinessDraftAssembler INSTANCE = Mappers.getMapper(InstBusinessDraftAssembler.class);


    InstBusinessDraftPO domain2Po(InstBusinessDraft domain);
    InstBusinessDraft po2Domain(InstBusinessDraftPO domain);

    List<InstBusinessDraft> po2DomainList(List<InstBusinessDraftPO> poList);

    @Mappings({
            @Mapping(target = "draftId", source = "draftId"),
            @Mapping(target = "businessKey", source = "businessKey"),
            @Mapping(target = "draftData", source = "draftData"),
            @Mapping(target = "businessType", expression = "java(BusinessTypeEnum.INST_CENTER)"),
            @Mapping(target = "moduleName", source = "moduleName"),
            @Mapping(target = "operateType", source = "operateType"),
            @Mapping(target = "status", expression = "java(InstProcessStatusEnum.PROCESSING)"),
            @Mapping(target = "owner", source = "shareId"),
    })
    InstBusinessDraft buildDefaultDraft(String draftId, OperateModuleEnum moduleName, OperateTypeEnum operateType, String businessKey, String draftData, String shareId);


    @Mappings({
            @Mapping(target = "draftId", source = "draftId"),
            @Mapping(target = "businessKey", source = "businessKey"),
            @Mapping(target = "draftData", source = "draftData"),
            @Mapping(target = "businessType", expression = "java(BusinessTypeEnum.INST_CENTER)"),
            @Mapping(target = "moduleName", source = "moduleName"),
            @Mapping(target = "operateType", source = "operateType"),
            @Mapping(target = "status", expression = "java(InstProcessStatusEnum.INIT)"),
            @Mapping(target = "owner", source = "shareId"),
    })
    InstBusinessDraft buildInitDraft(String draftId, OperateModuleEnum moduleName, OperateTypeEnum operateType, String businessKey, String draftData, String shareId);
}
