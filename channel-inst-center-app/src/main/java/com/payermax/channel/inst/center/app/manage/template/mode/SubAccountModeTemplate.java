package com.payermax.channel.inst.center.app.manage.template.mode;

import com.payermax.channel.inst.center.app.manage.template.activation.SubAccountActivationTemplate;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;

/**
 * <AUTHOR> at 2022/10/9 16:32
 **/
public interface SubAccountModeTemplate {

    /**
     * 子级资金账号-生成
     *
     * @param requestAccountDO
     */
    void getSubAccountNo(RequestAccountDO requestAccountDO);

    /**
     * 子级资金账号记录-创建
     *
     * @param requestAccountDO
     */
    InstSubFundsAccountEntity buildCreateSubAccountRecord(RequestAccountDO requestAccountDO);

    /**
     * 子级资金账户激活或进行其他处理
     *
     * @param requestAccountDO
     */
    void handleActivationOrOthersAction(RequestAccountDO requestAccountDO);

    /**
     * 获取机构子级资金账户激活模式
     *
     * @param activationMode
     */
    SubAccountActivationTemplate routeActivationTemplate(String activationMode);

}
