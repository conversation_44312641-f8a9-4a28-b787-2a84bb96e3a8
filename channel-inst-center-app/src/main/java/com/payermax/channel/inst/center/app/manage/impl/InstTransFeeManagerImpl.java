package com.payermax.channel.inst.center.app.manage.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstProductEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstTransFeeEntity;
import com.payermax.channel.inst.center.app.manage.InstProductManager;
import com.payermax.channel.inst.center.app.manage.InstTransFeeManager;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.request.InstProductReqDTO;
import com.payermax.channel.inst.center.app.request.InstTransFeeReqDTO;
import com.payermax.channel.inst.center.app.response.InstProductTransFeeVO;
import com.payermax.channel.inst.center.app.response.InstProductVO;
import com.payermax.channel.inst.center.app.response.InstTransFeeVO;
import com.payermax.channel.inst.center.app.service.InstContractProductService;
import com.payermax.channel.inst.center.app.service.InstProductCapabilityService;
import com.payermax.channel.inst.center.app.service.InstProductService;
import com.payermax.channel.inst.center.app.service.InstTransFeeService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName InstTransFeeManagerImpl
 * @Description
 * <AUTHOR>
 * @Date 2022/6/5 23:39
 */
@Service
public class InstTransFeeManagerImpl implements InstTransFeeManager {

    private static final String ALL_COUNTRY = "*";

    @Autowired
    private InstTransFeeService instTransFeeService;

    @Autowired
    private InstContractProductService instContractProductService;

    @Autowired
    private InstProductService instProductService;

    @Autowired
    private InstProductCapabilityService instProductCapabilityService;

    @Autowired
    private ReqDtoAssembler reqDtoAssembler;
    
    @Autowired
    private RespVoAssembler respVoAssembler;

    @Autowired
    private InstProductManager instProductManager;

    @NacosValue(value = "${instCenter.transFeeConfig.isAutoAddCapalibity:true}", autoRefreshed = true)
    private boolean isAutoAddCapalibity;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveAll(InstTransFeeReqDTO transFeeReqDTO) {
        InstContractProductEntity entity = new InstContractProductEntity();
        entity.setContractNo(transFeeReqDTO.getContractNo());
        entity.setInstProductCode(transFeeReqDTO.getProductCode());
        //查合同签约产品信息是否存在
        List<InstContractProductEntity> instContractProductEntities = instContractProductService.selectByEntity(entity);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(instContractProductEntities), "contract product is not exists");
        //检查保存交易费用的能力
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(transFeeReqDTO.getCountrys()), "countrys can not be null");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(transFeeReqDTO.getTargetOrgs()) || CollectionUtils.isNotEmpty(transFeeReqDTO.getCardOrgs()), "targetOrg or cardOrg can not be null");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(transFeeReqDTO.getTransFeeDTOList()), "transFee can not be null");
        //查产品下所有能力
        List<InstProductCapabilityEntity> productCapabilityEntities = instProductCapabilityService.getByProductCodes(Arrays.asList(transFeeReqDTO.getProductCode()));
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(productCapabilityEntities), "product capability is not exists");
        //产品下所有能力按国家分组
        Map<String, List<InstProductCapabilityEntity>> countryCapabilityMap = productCapabilityEntities.stream().collect(Collectors.groupingBy(InstProductCapabilityEntity::getCountry));
        //针对所有国家配置费用的特殊处理-20221020
        if(isAutoAddCapalibity){
            Optional<String> allCountry = transFeeReqDTO.getCountrys().stream().filter(country -> ALL_COUNTRY.equalsIgnoreCase(country)).findAny();
            if (allCountry.isPresent()) {
                List<InstProductCapabilityEntity> productCapabilityEntityList = countryCapabilityMap.get(ALL_COUNTRY);
                countryCapabilityMap = saveAllCountryCapability(transFeeReqDTO,productCapabilityEntityList,countryCapabilityMap);
            } else {
                for (String country : transFeeReqDTO.getCountrys()) {
                    List<InstProductCapabilityEntity> productCapabilityList = countryCapabilityMap.get(country);
                    countryCapabilityMap = saveAllCountryCapability(transFeeReqDTO,productCapabilityList,countryCapabilityMap);
                }
            }
        }
        List<String> allCapabilityCodes = new ArrayList<>();
        //根据上送的国家及目标机构或卡组织 确认当前要配置交易费用的能力
        for (String country : transFeeReqDTO.getCountrys()) {
            //根据上送的国家获取对应的产品能力
            List<InstProductCapabilityEntity> productCapabilityEntityList = countryCapabilityMap.get(country);
            List<String> capabilityCodes = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(transFeeReqDTO.getTargetOrgs()) && StringUtils.isNotBlank(transFeeReqDTO.getTargetOrgs().get(0))){ //NO_CHECK 方法未被调用
                capabilityCodes = productCapabilityEntityList.stream().filter(obj -> transFeeReqDTO.getTargetOrgs().contains(obj.getTargetOrg())).map(InstProductCapabilityEntity::getCapabilityCode).distinct().collect(Collectors.toList());
            }else{
                capabilityCodes = productCapabilityEntityList.stream().filter(obj -> transFeeReqDTO.getCardOrgs().contains(obj.getCardOrg())).map(InstProductCapabilityEntity::getCapabilityCode).distinct().collect(Collectors.toList());
            }
            allCapabilityCodes.addAll(capabilityCodes);
        }
        //根据产品编码和上送的所有能力查对应的签约产品能力信息
        List<InstContractProductEntity> instContractProductEntityList = instContractProductService.selectByProductCodeAndCapabilityCodes(transFeeReqDTO.getProductCode(), allCapabilityCodes);
        //签约能力交易费用分组id更新
        String feeGroupId = UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
        instContractProductService.updateFeeGroupId(transFeeReqDTO.getContractNo(),transFeeReqDTO.getProductCode(),allCapabilityCodes,feeGroupId);
        //删除上送能力中已配置的交易费用
        List<String> existsFeeGroupIds = instContractProductEntityList.stream().filter(obj -> StringUtils.isNotBlank(obj.getFeeGroupId())).map(InstContractProductEntity::getFeeGroupId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(existsFeeGroupIds)){
            //查询该交易费用是否有别的能力在用
            Set<String> set = instContractProductEntities.stream().filter(obj -> existsFeeGroupIds.contains(obj.getFeeGroupId())).map(InstContractProductEntity::getInstProductCapabilityCode).collect(Collectors.toSet());
            if(allCapabilityCodes.containsAll(set)){
                instTransFeeService.deleteByFeeGroupIds(existsFeeGroupIds);
            }
        }
        //新增上送能力对应的交易费用
        List<InstTransFeeEntity> instTransFeeEntities = reqDtoAssembler.toInstTransFeeEntities(transFeeReqDTO.getTransFeeDTOList());
        List<InstTransFeeEntity> finalInstTransFeeEntities = instTransFeeEntities.stream().map(transFeeEntity -> {
            transFeeEntity.setFeeGroupId(feeGroupId);
            return transFeeEntity;
        }).collect(Collectors.toList());
        int result = instTransFeeService.saveBatch(finalInstTransFeeEntities);
        return result;
    }

    private Map<String, List<InstProductCapabilityEntity>> saveAllCountryCapability(InstTransFeeReqDTO transFeeReqDTO,List<InstProductCapabilityEntity> productCapabilityEntityList,Map<String, List<InstProductCapabilityEntity>> countryCapabilityMap) {
        if(CollectionUtils.isEmpty(productCapabilityEntityList)){
            return saveCapability(transFeeReqDTO, transFeeReqDTO.getTargetOrgs(), transFeeReqDTO.getCardOrgs());
        }
        if(CollectionUtils.isNotEmpty(transFeeReqDTO.getTargetOrgs())){
            List<String> targetOrgs = productCapabilityEntityList.stream().map(InstProductCapabilityEntity::getTargetOrg).distinct().collect(Collectors.toList());
            List<String> notExistsTargetOrgs = transFeeReqDTO.getTargetOrgs().stream().filter(obj -> !targetOrgs.contains(obj)).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(notExistsTargetOrgs)){
                return saveCapability(transFeeReqDTO,notExistsTargetOrgs,null);
            }
        }
        if(CollectionUtils.isNotEmpty(transFeeReqDTO.getCardOrgs())){
            List<String> cardOrgs = productCapabilityEntityList.stream().map(InstProductCapabilityEntity::getCardOrg).distinct().collect(Collectors.toList());
            List<String> notExistsCardOrgs = transFeeReqDTO.getCardOrgs().stream().filter(obj -> !cardOrgs.contains(obj)).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(notExistsCardOrgs)){
                return saveCapability(transFeeReqDTO,null,notExistsCardOrgs);
            }
        }
        return countryCapabilityMap;
    }

    private Map<String, List<InstProductCapabilityEntity>> saveCapability(InstTransFeeReqDTO transFeeReqDTO,List<String> targetOrgs,List<String> cardOrgs) {
        InstProductReqDTO instProductReqDTO = new InstProductReqDTO();
        instProductReqDTO.setContractNo(transFeeReqDTO.getContractNo());
        instProductReqDTO.setProductCode(transFeeReqDTO.getProductCode());
        instProductReqDTO.setExtraInfo(ALL_COUNTRY);
        instProductReqDTO.setCountrys(transFeeReqDTO.getCountrys());
        instProductReqDTO.setTargetOrgs(targetOrgs);
        instProductReqDTO.setCardOrgs(cardOrgs);
        instProductManager.saveAll(instProductReqDTO);
        List<InstProductCapabilityEntity> productCapabilityEntities = instProductCapabilityService.getByProductCodes(Arrays.asList(transFeeReqDTO.getProductCode()));
        return productCapabilityEntities.stream().collect(Collectors.groupingBy(InstProductCapabilityEntity::getCountry));
    }

    @Override
    public List<InstProductTransFeeVO> queryAll(InstTransFeeReqDTO transFeeReqDTO) {
        InstContractProductEntity entity = new InstContractProductEntity();
        entity.setContractNo(transFeeReqDTO.getContractNo());
        if(StringUtils.isNotBlank(transFeeReqDTO.getProductCode())){
            entity.setInstProductCode(transFeeReqDTO.getProductCode());
        }
        //根据合同单号查所有已签约产品
        List<InstContractProductEntity> instContractProductEntities = instContractProductService.selectByEntity(entity);
        if(CollectionUtils.isEmpty(instContractProductEntities)){
            return null;
        }
        //所有签约产品编码
        List<String> productCodes = instContractProductEntities.stream().map(InstContractProductEntity::getInstProductCode).distinct().collect(Collectors.toList());
        //已签约所有产品
        List<InstProductEntity> instProductEntities = instProductService.getByProductCodes(productCodes);
        List<InstProductVO> instProductVOs = respVoAssembler.toInstProductVos(instProductEntities);
        //未配置交易费用的所有签约产品能力
        List<String> noFeeGroupIdCapabilities = instContractProductEntities.stream().filter(obj -> StringUtils.isBlank(obj.getFeeGroupId())).map(InstContractProductEntity::getInstProductCapabilityCode).collect(Collectors.toList());
        Map<String, List<InstProductCapabilityEntity>> noFeeGroupIdCapabilityMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(noFeeGroupIdCapabilities)){
            List<InstProductCapabilityEntity> noFeeGroupIdProductCapabilities = instProductCapabilityService.getByCapabilityCodes(noFeeGroupIdCapabilities);
            //未配置交易费用的签约产品能力（按产品编码分组）
            noFeeGroupIdCapabilityMap = noFeeGroupIdProductCapabilities.stream().collect(Collectors.groupingBy(InstProductCapabilityEntity::getInstProductCode));
        }

        //已配置交易费用的所有签约产品能力
        Map<String, List<InstContractProductEntity>> hasFeeGroupContractProductMap = instContractProductEntities.stream().filter(obj -> StringUtils.isNotBlank(obj.getFeeGroupId())).collect(Collectors.groupingBy(InstContractProductEntity::getFeeGroupId));
        //key:feeGroupId
        Map<String, List<InstProductCapabilityEntity>> hasFeeGroupIdCapabilityMap = new HashMap<>();
        for (Map.Entry<String, List<InstContractProductEntity>> entry : hasFeeGroupContractProductMap.entrySet()) {
            List<InstProductCapabilityEntity> hasFeeGroupIdProductCapabilities = instProductCapabilityService.getByCapabilityCodes(entry.getValue().stream().map(InstContractProductEntity::getInstProductCapabilityCode).collect(Collectors.toList()));
            hasFeeGroupIdCapabilityMap.put(entry.getKey(),hasFeeGroupIdProductCapabilities);
        }
        //已配置交易费用的所有合同签约产品信息(按产品编码分组)
        Map<String, List<InstContractProductEntity>> hasFeeGroupIdMap = instContractProductEntities.stream().filter(obj -> StringUtils.isNotBlank(obj.getFeeGroupId())).collect(Collectors.groupingBy(InstContractProductEntity::getInstProductCode));
        Map<String,List<String>> productFeeGroupIdMap = new HashMap<>();
        for (Map.Entry<String, List<InstContractProductEntity>> entry : hasFeeGroupIdMap.entrySet()) {
            productFeeGroupIdMap.put(entry.getKey(),entry.getValue().stream().map(InstContractProductEntity::getFeeGroupId).distinct().collect(Collectors.toList()));
        }

        //已配置交易费用的所有费用分组id
        List<String> feeGroupIds = instContractProductEntities.stream().filter(obj->StringUtils.isNotBlank(obj.getFeeGroupId())).map(InstContractProductEntity::getFeeGroupId).distinct().collect(Collectors.toList());
        //已配置的所有交易费用（按费用分组id分组）
        Map<String, List<InstTransFeeVO>> feeGroupIdMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(feeGroupIds)){
            //已配置的所有交易费用
            List<InstTransFeeEntity> transFeeEntityList = instTransFeeService.getByFeeGroupIds(feeGroupIds);
            List<InstTransFeeVO> instTransFeeVOs = respVoAssembler.toInstTransFeeVos(transFeeEntityList);
            feeGroupIdMap = instTransFeeVOs.stream().collect(Collectors.groupingBy(InstTransFeeVO::getFeeGroupId));
        }

        Map<String, List<InstProductCapabilityEntity>> finalNoFeeGroupIdCapabilityMap = noFeeGroupIdCapabilityMap;
        Map<String, List<InstTransFeeVO>> finalFeeGroupIdMap = feeGroupIdMap;
        List<InstProductTransFeeVO> productTransFeeVOList = instProductVOs.stream().sorted(Comparator.comparing(InstProductVO::getChannelType)).map(productVO -> {
            InstProductTransFeeVO productTransFeeVO = new InstProductTransFeeVO();
            //产品基本信息
            productTransFeeVO.setContractNo(transFeeReqDTO.getContractNo());
            BeanUtils.copyProperties(productVO, productTransFeeVO);
            //产品未配置交易费用的能力
            List<InstProductCapabilityEntity> noFeeGroupIdProductCapabilityEntityList = finalNoFeeGroupIdCapabilityMap.get(productVO.getProductCode());
            if(CollectionUtils.isNotEmpty(noFeeGroupIdProductCapabilityEntityList)){
                productTransFeeVO.setCountrys(noFeeGroupIdProductCapabilityEntityList.stream().filter(obj->StringUtils.isNotBlank(obj.getCountry())).map(InstProductCapabilityEntity::getCountry).distinct().collect(Collectors.toList()));
                productTransFeeVO.setTargetOrgs(noFeeGroupIdProductCapabilityEntityList.stream().filter(obj->StringUtils.isNotBlank(obj.getTargetOrg())).map(InstProductCapabilityEntity::getTargetOrg).distinct().collect(Collectors.toList()));
                productTransFeeVO.setCardOrgs(noFeeGroupIdProductCapabilityEntityList.stream().filter(obj->StringUtils.isNotBlank(obj.getCardOrg())).map(InstProductCapabilityEntity::getCardOrg).distinct().collect(Collectors.toList()));

            }
            //产品已配置交易费用的能力
            List<String> feeGroupIdList = productFeeGroupIdMap.get(productVO.getProductCode());
            if(CollectionUtils.isNotEmpty(feeGroupIdList) && CollectionUtils.isNotEmpty(hasFeeGroupIdCapabilityMap)){
                List<InstProductTransFeeVO.ProductCapabilityTransFeeVO> productCapabilityTransFeeVOList = feeGroupIdList.stream().map(feeGroupId -> {
                    InstProductTransFeeVO.ProductCapabilityTransFeeVO productCapabilityTransFeeVO = new InstProductTransFeeVO.ProductCapabilityTransFeeVO();
                    List<InstProductCapabilityEntity> productCapabilityEntityList = hasFeeGroupIdCapabilityMap.get(feeGroupId);
                    productCapabilityTransFeeVO.setCountrys(productCapabilityEntityList.stream().filter(obj->StringUtils.isNotBlank(obj.getCountry())).map(InstProductCapabilityEntity::getCountry).distinct().collect(Collectors.joining(",")));
                    productCapabilityTransFeeVO.setTargetOrgs(productCapabilityEntityList.stream().filter(obj->StringUtils.isNotBlank(obj.getTargetOrg())).map(InstProductCapabilityEntity::getTargetOrg).distinct().collect(Collectors.joining(",")));
                    productCapabilityTransFeeVO.setCardOrgs(productCapabilityEntityList.stream().filter(obj->StringUtils.isNotBlank(obj.getCardOrg())).map(InstProductCapabilityEntity::getCardOrg).distinct().collect(Collectors.joining(",")));
                    productCapabilityTransFeeVO.setFeeGroupId(feeGroupId);
                    List<InstTransFeeVO> instTransFeeVOS = finalFeeGroupIdMap.get(feeGroupId);
                    productCapabilityTransFeeVO.setUtcCreate(instTransFeeVOS.get(0).getUtcCreate()); //NO_CHECK 方法未被调用
                    productCapabilityTransFeeVO.setUtcModified(instTransFeeVOS.get(0).getUtcModified()); //NO_CHECK 方法未被调用
                    productCapabilityTransFeeVO.setTransFeeVOList(instTransFeeVOS);
                    return productCapabilityTransFeeVO;
                }).collect(Collectors.toList());
                productTransFeeVO.setProductCapabilityTransFeeVOList(productCapabilityTransFeeVOList);
            }
            return productTransFeeVO;
        }).collect(Collectors.toList());
        return productTransFeeVOList;
    }

    @Override
    public int delete(InstTransFeeReqDTO transFeeReqDTO) {
        Preconditions.checkArgument(StringUtils.isNotBlank(transFeeReqDTO.getContractNo()) && StringUtils.isNotBlank(transFeeReqDTO.getProductCode()) && StringUtils.isNotBlank(transFeeReqDTO.getFeeGroupId()), "transfee info can not be null");
        InstContractProductEntity entity = new InstContractProductEntity();
        entity.setContractNo(transFeeReqDTO.getContractNo());
        entity.setInstProductCode(transFeeReqDTO.getProductCode());
        entity.setFeeGroupId(transFeeReqDTO.getFeeGroupId());
        //查合同签约产品信息是否存在
        List<InstContractProductEntity> instContractProductEntities = instContractProductService.selectByEntity(entity);
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(instContractProductEntities), "contract product fee config is not exists");
        //更新能力费用配置
        List<String> capabilityCodes = instContractProductEntities.stream().map(InstContractProductEntity::getInstProductCapabilityCode).distinct().collect(Collectors.toList());
        instContractProductService.updateFeeGroupId(transFeeReqDTO.getContractNo(),transFeeReqDTO.getProductCode(),capabilityCodes,StringUtils.EMPTY);
        //删除已配置费用
        int result = instTransFeeService.deleteByFeeGroupIds(Arrays.asList(transFeeReqDTO.getFeeGroupId()));
        return result;
    }
}
