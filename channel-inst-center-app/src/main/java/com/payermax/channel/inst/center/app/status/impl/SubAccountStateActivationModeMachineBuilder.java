package com.payermax.channel.inst.center.app.status.impl;

import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.app.status.IStateMachineBuilder;
import com.payermax.channel.inst.center.app.status.StateConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.instcenter.ActivationModeEnum;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.common.lang.exception.BusinessException;
import com.ushareit.fintech.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> at 2022/10/9 21:44
 **/
@Slf4j
@Component
public class SubAccountStateActivationModeMachineBuilder implements IStateMachineBuilder<SubAccountStatusEnum, ActivationModeEnum> {

    private static final String ACTIVATION_MODE_ID = "ACTIVATION_MODE_ID";

    @Autowired
    InstSubFundsAccountService instSubFundsAccountService;

    @Override
    public StateMachine<SubAccountStatusEnum, ActivationModeEnum> build(SubAccountStatusEnum initState, BeanFactory beanFactory) throws Exception {
        StateMachineBuilder.Builder<SubAccountStatusEnum, ActivationModeEnum> builder = StateMachineBuilder.builder();

        builder.configureConfiguration().withConfiguration().autoStartup(true).machineId(ACTIVATION_MODE_ID)
                .beanFactory(beanFactory);

        builder.configureStates().withStates().initial(initState)
                .end(SubAccountStatusEnum.TERMINATED).state(SubAccountStatusEnum.TO_BE_ACTIVATED);

        builder.configureTransitions()
                .withExternal().source(SubAccountStatusEnum.TO_BE_ACTIVATED).target(SubAccountStatusEnum.TO_BE_ACTIVATED)
                .event(ActivationModeEnum.API).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.TO_BE_ACTIVATED).target(SubAccountStatusEnum.ACTIVE)
                .event(ActivationModeEnum.API).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.TO_BE_ACTIVATED).target(SubAccountStatusEnum.TO_BE_ACTIVATED)
                .event(ActivationModeEnum.OFFLINE).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.TO_BE_ACTIVATED).target(SubAccountStatusEnum.ACTIVE)
                .event(ActivationModeEnum.OFFLINE).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVE).target(SubAccountStatusEnum.ACTIVE)
                .event(ActivationModeEnum.API).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVE).target(SubAccountStatusEnum.ACTIVATED)
                .event(ActivationModeEnum.API).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVE).target(SubAccountStatusEnum.TERMINATED)
                .event(ActivationModeEnum.API).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVE).target(SubAccountStatusEnum.ACTIVE)
                .event(ActivationModeEnum.OFFLINE).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVE).target(SubAccountStatusEnum.ACTIVATED)
                .event(ActivationModeEnum.OFFLINE).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVE).target(SubAccountStatusEnum.TERMINATED)
                .event(ActivationModeEnum.OFFLINE).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVATED).target(SubAccountStatusEnum.ACTIVATED)
                .event(ActivationModeEnum.API).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVATED).target(SubAccountStatusEnum.TERMINATED)
                .event(ActivationModeEnum.API).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVATED).target(SubAccountStatusEnum.ACTIVATED)
                .event(ActivationModeEnum.OFFLINE).action(this.successAction(), this.errorAction()).and()
                .withExternal().source(SubAccountStatusEnum.ACTIVATED).target(SubAccountStatusEnum.TERMINATED)
                .event(ActivationModeEnum.OFFLINE).action(this.successAction(), this.errorAction()).and();

        return builder.build();


    }

    /**
     * 状态机成功Action
     *
     * @return
     */
    private Action<SubAccountStatusEnum, ActivationModeEnum> successAction() {
        return stateContext -> {
            Integer origStatus = stateContext.getExtendedState().get(StateConstants.VAR_KEY_ORIG_STATUS, Integer.class);
            InstSubFundsAccountEntity subFundsAccountEntity = stateContext.getExtendedState().get(InstSubFundsAccountEntity.class, InstSubFundsAccountEntity.class);
            String eventName = stateContext.getEvent().name();
            int result = instSubFundsAccountService.update(origStatus, subFundsAccountEntity);
            // 说明乐观锁导致更新失败
            if (result == 0) {
                log.warn("sub account update fail. origStatus = {}, ,eventName = {}, subAccount = {}, stateMachine ={}", origStatus, eventName, JsonUtils.toString(subFundsAccountEntity), this.getClass().getSimpleName());
                throw new BusinessException(ErrorCodeEnum.UPDATE_SUB_ACCOUNT_FAIL.getCode(), eventName + " event sub account update fail");
            }
        };
    }

    /**
     * 状态机失败action
     *
     * @return
     */
    private Action<SubAccountStatusEnum, ActivationModeEnum> errorAction() {
        return stateContext -> {
            stateContext.getExtendedState().getVariables().put(Exception.class, stateContext.getException());
        };
    }
}
