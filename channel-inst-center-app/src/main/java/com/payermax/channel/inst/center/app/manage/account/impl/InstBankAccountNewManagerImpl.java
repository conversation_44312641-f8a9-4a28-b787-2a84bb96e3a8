package com.payermax.channel.inst.center.app.manage.account.impl;

import com.payermax.channel.inst.center.app.assembler.domain.InstBankAccountAssembler;
import com.payermax.channel.inst.center.app.manage.account.InstBankAccountNewManager;
import com.payermax.channel.inst.center.app.manage.account.processHandler.InstBankAccountSaveHandler;
import com.payermax.channel.inst.center.app.manage.account.processHandler.InstBankAccountUpdateHandler;
import com.payermax.channel.inst.center.app.request.InstBankAccountReqDTO;
import com.payermax.channel.inst.center.app.request.account.InstBankAccountSaveOrUpdateRequest;
import com.payermax.channel.inst.center.app.response.InstBankAccountVO;
import com.payermax.channel.inst.center.domain.entity.account.InstBankAccount;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBankAccountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/27
 * @DESC
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InstBankAccountNewManagerImpl implements InstBankAccountNewManager {

    private final InstBankAccountSaveHandler saveHandler;
    private final InstBankAccountUpdateHandler updateHandler;
    private final InstBankAccountRepository accountRepository;
    private final InstBankAccountAssembler bankAccountAssembler;


    @Override
    public Boolean startSaveProcess(InstBankAccountSaveOrUpdateRequest request, String shareId) {
        return saveHandler.startSaveProcess(request, shareId);
    }

    @Override
    public Boolean startUpdateProcess(InstBankAccountSaveOrUpdateRequest request, String shareId) {
        return updateHandler.startUpdateProcess(request, shareId);
    }

    @Override
    public InstBankAccountVO queryAccountById(InstBankAccountReqDTO request) {
        InstBankAccount bankAccount = bankAccountAssembler.po2Domain(accountRepository.getById(request.getId()));
        return bankAccountAssembler.domain2Vo(bankAccount);
    }

    @Override
    public List<InstBankAccountVO> queryAllowDelete(InstBankAccountReqDTO request) {
        return bankAccountAssembler.po2VoList(accountRepository.queryByConditions(bankAccountAssembler.request2Po(request)));
    }


}
