package com.payermax.channel.inst.center.app.manage;


import com.payermax.channel.inst.center.common.result.PageResult;
import com.payermax.channel.inst.center.app.request.InstBaseInfoReqDTO;
import com.payermax.channel.inst.center.app.request.InstBrandReqDTO;
import com.payermax.channel.inst.center.app.request.InstInfoReqDTO;
import com.payermax.channel.inst.center.app.response.InstBaseInfoVO;

import java.util.List;

/**
 * 机构信息相关服务Manager
 *
 * <AUTHOR>
 * @date 2022/5/18 22:32
 */
public interface InstBaseInfoManager {

    /**
     * 查询机构信息
     *
     * @return
     */
    List<InstBaseInfoVO> query(InstBaseInfoReqDTO instBaseInfoReqDTO);

    /**
     * 查询机构信息
     *
     * @return
     */
    PageResult<InstBaseInfoVO> queryAll(InstBaseInfoReqDTO instBaseInfoReqDTO);

    /**
     * 查询指定BD负责的机构信息
     *
     * @return
     */
    List<InstBaseInfoVO> queryByBdId(String bdId);

    /**
     * 查询指定BD负责的机构信息
     *
     * @return
     */
    List<InstBaseInfoVO> queryByBdIds(List<String> bdIds);

    /**
     * 保存机构信息
     *
     * @return
     */
    int save(InstBaseInfoReqDTO instBaseInfoReqDTO);

    /**
     * 更新机构编码
     *
     * @param instBaseInfoReqDTO
     * @return
     */
    int updateInstCode(InstBaseInfoReqDTO instBaseInfoReqDTO);

    /**
     * 查指定品牌下的机构名称列表
     *
     * @param instBrandReqDTO
     * @return
     */
    List<InstBaseInfoVO> queryInstNameList(InstBrandReqDTO instBrandReqDTO);

    /**
     * 新增机构信息
     *
     * @return
     */
    int add(InstInfoReqDTO instInfoReqDTO);

    /**
     * 检查机构信息（主体所在地+公司注册号）
     *
     * @return
     */
    int check(InstBaseInfoReqDTO instBaseInfoReqDTO);
}
