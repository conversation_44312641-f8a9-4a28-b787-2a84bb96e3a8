package com.payermax.channel.inst.center.app.manage.account.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.manage.account.InstFundsAccountNewManager;
import com.payermax.channel.inst.center.app.manage.account.processHandler.InstFundsAccountSaveHandler;
import com.payermax.channel.inst.center.infrastructure.adapter.InstFundsAccountSaveAdapter;
import com.payermax.omc.channel.exchange.facade.request.InstFundsAccountSaveRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/10/9
 * @DESC
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InstFundsAccountNewManagerImpl implements InstFundsAccountNewManager {

    private final InstFundsAccountSaveHandler instFundsAccountSaveHandler;
    private final InstFundsAccountSaveAdapter instFundsAccountSaveAdapter;

    @NacosValue(value = "${omc.workflow.process.instFundsAccount.save.newWorkflowEnable:true}", autoRefreshed = true)
    private Boolean newWorkflowEnable;

    @Override
    public Boolean startSaveProcess(InstFundsAccountSaveRequest request, String shareId) {
        log.info("账户保存 OA 发起-是否启用新工作流:{}", newWorkflowEnable);
        if(newWorkflowEnable){
            log.info("账户保存 OA 发起-新工作流");
            return instFundsAccountSaveHandler.startSaveProcess(request, shareId);
        }else {
            log.info("账户保存 OA 发起-钉钉 OA");
            request.setNewWorkflow(false);
            instFundsAccountSaveAdapter.saveWithProcess(shareId, request);
            return Boolean.TRUE;
        }

    }
}
