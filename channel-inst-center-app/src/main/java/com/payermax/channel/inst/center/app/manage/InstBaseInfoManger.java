package com.payermax.channel.inst.center.app.manage;

import com.payermax.channel.inst.center.domain.subaccount.request.QueryInstBrandRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QueryInstBrandResponseDO;
import com.payermax.channel.inst.center.facade.request.QueryBrandByChannelCodeRequest;
import com.payermax.channel.inst.center.facade.response.InstBaseInfoWithBrandResponse;

import java.util.List;

public interface InstBaseInfoManger {

    /**
     * 查询全量机构code列表信息
     */
    List<String> listInstCode();

    /**
     * 机构品牌列表查询
     * @param queryInstBrandRequestDO
     * @return
     */
    List<QueryInstBrandResponseDO> listInstBrand(QueryInstBrandRequestDO queryInstBrandRequestDO);

    /**
     * 根据 channelCode 查询机构及品牌，channelCode 不存在时直接返回
     * @param queryBrandByChannelCodeRequest
     * @return
     */
    List<InstBaseInfoWithBrandResponse> listByChannelCode(QueryBrandByChannelCodeRequest queryBrandByChannelCodeRequest);
}
