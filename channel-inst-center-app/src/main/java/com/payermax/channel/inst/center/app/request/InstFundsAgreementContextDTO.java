package com.payermax.channel.inst.center.app.request;

import com.payermax.channel.inst.center.common.enums.fundsagreement.*;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC
 */
@Data
public class InstFundsAgreementContextDTO {

    /**
     * 业务协议
     */
    @NotNull
    private BizAgreement bizAgreement;

    /**
     * 资金协议
     */
    @NotNull
    private FundsAgreement fundsAgreement;

    /**
     * 结算规则
     */
    @NotNull
    private List<FundsSettleRule> settleRules;

    @Data
    public static class BizAgreement {

        /**
         * 业务协议单号
         */
        private String bizAgreementNo;

        /**
         * 协议名称
         */
        @NotBlank
        private String name;

        /**
         * 业务协议类型
         */
        @NotEmpty
        private InstBizAgreementTypeEnum type;

        /**
         * 协议发起方
         */
        @NotBlank
        private String initiator;

        /**
         * 协议对手方
         */
        @NotBlank
        private String counter;

        /**
         * 机构合约号-外部机构协议时不为空
         */
        private String contractNo;
    }

    @Data
    public static class FundsAgreement {

        /**
         * 资金协议单号
         */
        private String fundsAgreementNo;

        /**
         * 业务协议单号
         */
        private String bizAgreementNo;

        /**
         * 协议名称
         */
        @NotBlank
        private String name;

        /**
         * 资金协议类型
         */
        @NotEmpty
        private InstFundsAgreementTypeEnum type;

        /**
         * 清算币种
         */
        @NotBlank
        private String clearingCcy;

        /**
         * 清算模式
         */
        @NotEmpty
        private InstFundsAgreementClearingPatternEnum clearingPattern;

        /**
         * 支付方式
         */
        private String paymentMethod;

        /**
         * 目标机构
         */
        private String targetOrg;

        /**
         * 卡组
         */
        private String cardOrg;

        /**
         * 渠道MID
         */
        private String mid;

        /**
         * 生效状态
         */
        private InstFundsAgreementStatusEnum status;

        /**
         * 备注
         */
        private String memo;
    }

    @Data
    public static class FundsSettleRule {

        /**
         * 清算规则编号
         */
        private String settleRuleNo;

        /**
         * 资金协议单号
         */
        private String fundsAgreementNo;

        /**
         * 时区-例:UTC8
         */
        private String timezone;

        /**
         * 切点-例:16:00:00
         */
        private String cutoffTime;

        /**
         * 清分范围开始
         */
        @NotEmpty
        private String clearingRangeStart;

        /**
         * 清分范围结束
         */
        @NotEmpty
        private String clearingRangeEnd;


        /**
         * 清分模式-主动/被动
         */
        @NotEmpty
        private InstFundsSettleRulePatternEnum clearingPattern;

        /**
         * 清偿模式-主动/被动
         */
        @NotEmpty
        private InstFundsSettleRulePatternEnum clearOffPattern;

        /**
         * 清偿时间-出账单的规则-D1
         */
        @NotEmpty
        private String clearOffTime;

        /**
         * 清偿类型-外部清偿/内部清偿
         */
        @NotEmpty
        private InstFundsSettleRuleClearOffTypeEnum clearOffType;

        /**
         * 起结金额
         */
        private BigDecimal settleMinAmount;

        /**
         * 结算币种
         */
        @NotEmpty
        private String settleCcy;

        /**
         * 结算账户
         */
        @NotEmpty
        private String settleAccount;

        /**
         * 到账时间-D+3WD
         */
        @NotEmpty
        private String settleArrived;

        /**
         * 换汇时间-D+3WD
         */
        private String settleExchange;

        /**
         * 到账时间-D+3WD
         */
        private String settlePayment;

    }
}
