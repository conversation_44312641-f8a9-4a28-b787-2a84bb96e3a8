package com.payermax.channel.inst.center.app.manage.account;

import com.payermax.channel.inst.center.app.request.InstBankAccountReqDTO;
import com.payermax.channel.inst.center.app.request.account.InstBankAccountSaveOrUpdateRequest;
import com.payermax.channel.inst.center.app.response.InstBankAccountVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/27
 * @DESC 银行账户管理-新
 */
public interface InstBankAccountNewManager {

    /**
     * 发起新增账户流程
     * @param request 请求参数
     * @param shareId 用户 ID
     * @return 流程发起结果
     */
    Boolean startSaveProcess(InstBankAccountSaveOrUpdateRequest request, String shareId);

    /**
     * 发起修改账户流程
     * @param request 请求参数
     * @param shareId 用户 ID
     * @return 流程发起结果
     */
    Boolean startUpdateProcess(InstBankAccountSaveOrUpdateRequest request, String shareId);

    /**
     * 根据账户 ID 查询账户详情
     * @param request 账户查询信息
     * @return 账户详情
     */
    InstBankAccountVO queryAccountById(InstBankAccountReqDTO request);

    /**
     * 根据机构查询，包括软删除账户
     */
    List<InstBankAccountVO> queryAllowDelete(InstBankAccountReqDTO request);


}
