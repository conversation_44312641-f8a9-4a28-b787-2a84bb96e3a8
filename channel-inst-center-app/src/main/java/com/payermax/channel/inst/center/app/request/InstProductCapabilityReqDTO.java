package com.payermax.channel.inst.center.app.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstProductCapabilityReqDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/26 20:16
 */
@Data
public class InstProductCapabilityReqDTO implements Serializable {

    private static final long serialVersionUID = 4716134111045598246L;

    @ApiModelProperty(notes = "产品能力编码")
    private String capabilityCode;

    @ApiModelProperty(notes = "产品编码")
    private String instProductCode;

    @ApiModelProperty(notes = "目标机构")
    private String targetOrg;

    @ApiModelProperty(notes = "卡组织")
    private String cardOrg;

    @ApiModelProperty(notes = "国家")
    private String country;

    @ApiModelProperty(notes = "币种")
    private String currency;

    @ApiModelProperty(notes = "金额倍数限制")
    private String amountMultipleLimit;

    @ApiModelProperty(notes = "单笔限额")
    private String amountSingleLimit;

    @ApiModelProperty(notes = "日累计限额")
    private String amountDayLimit;

    @ApiModelProperty(notes = "月累计限额")
    private String amountMonthLimit;

    @ApiModelProperty(notes = "支付工具")
    private String paymentTool;

    @ApiModelProperty(notes = "支付流程")
    private String paymentFlow;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    @ApiModelProperty(notes = "结算扩展数据 json")
    private String settlementExtraInfo;

    @ApiModelProperty(notes = "扩展数据 json")
    private String extraInfo;

    /**
     * 一个国家对应的一组能力（即一组目标机构）
     */
    @ApiModelProperty(notes = "目标机构集合，多个直接用逗号相连")
    private String targetOrgs;

    /**
     * 一次能力操作对应的卡组织
     */
    @ApiModelProperty(notes = "卡组织集合，多个直接用逗号相连")
    private String cardOrgs;

    /**
     * 对应一次操作的版本
     */
    private String version;
}
