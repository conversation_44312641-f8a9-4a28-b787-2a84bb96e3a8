package com.payermax.channel.inst.center.app.manage.contract;

import com.payermax.channel.inst.center.app.dto.contract.InstContractVersionInfoDto;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.request.contract.InstContractAccountingTypeModifyRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractAiParseQueryRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractInfoQueryRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractVersionUpgradeRequest;
import com.payermax.channel.inst.center.app.response.InstContractAiParseQueryResponse;
import com.payermax.common.lang.model.dto.response.RowResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/9
 * @DESC 机构合约合同管理
 */
public interface InstContractManageService {



    /**
     * 查询合约及版本信息
     */
    RowResponse<InstContractVersionInfoDto> queryInstContractInfo(InstContractInfoQueryRequest request);

    /**
     * 更新渠道核对方式
     */
    Boolean updateAccountingType(InstContractAccountingTypeModifyRequest request, String operator);

    /**
     * 合约版本升级
     */
    Boolean contractVersionUpgrade(InstContractVersionUpgradeRequest request, String operator);

    /**
     * 下载合约模板
     */
    String contractTemplateDownload(String contractBizType);

    /**
     * 刷新合约缓存
     */
    Boolean instContractCacheRefresh();

    /**
     * 获取 AI 解析列表
     */
    List<InstContractAiParseQueryResponse> getAiParseList(InstContractAiParseQueryRequest request);

    /**
     * 机构合约合同智能解析
     */
    String contractAiParse(InstContractParseRequestDTO request);
}
