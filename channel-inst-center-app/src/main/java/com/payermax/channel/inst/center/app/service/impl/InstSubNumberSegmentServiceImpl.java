package com.payermax.channel.inst.center.app.service.impl;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.app.service.InstSubNumberSegmentService;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubNumberSegmentEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubNumberSegmentQueryEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstSubNumberSegmentDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> at 2022/10/8 17:45
 **/
@Service
public class InstSubNumberSegmentServiceImpl implements InstSubNumberSegmentService {

    @Autowired
    InstSubNumberSegmentDao instSubNumberSegmentDao;

    @Override
    public int updateById(InstSubNumberSegmentEntity instSubNumberSegmentEntity, String originalMaxUsed) {
        return instSubNumberSegmentDao.updateByPrimaryKeySelective(instSubNumberSegmentEntity, originalMaxUsed);
    }

    @Override
    public List<InstSubNumberSegmentEntity> queryListByAccountId(InstSubNumberSegmentQueryEntity instSubNumberSegmentQueryEntity) {
        Preconditions.checkArgument(instSubNumberSegmentQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(instSubNumberSegmentQueryEntity.getAccountId()), "param accountId is mandatory");
        // 可用号段列表
        return instSubNumberSegmentDao.selectByRecord(instSubNumberSegmentQueryEntity);
    }

    @Override
    public Integer queryCountByAccountId(InstSubNumberSegmentQueryEntity instSubNumberSegmentQueryEntity) {
        Preconditions.checkArgument(instSubNumberSegmentQueryEntity != null, "param record is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(instSubNumberSegmentQueryEntity.getAccountId()), "param accountId is mandatory");
        // 可用号段列表
        return instSubNumberSegmentDao.selectCountByRecord(instSubNumberSegmentQueryEntity);
    }
}
