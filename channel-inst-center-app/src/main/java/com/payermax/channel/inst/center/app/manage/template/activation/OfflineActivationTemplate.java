package com.payermax.channel.inst.center.app.manage.template.activation;

import com.payermax.channel.inst.center.app.status.StateMachineExecutor;
import com.payermax.channel.inst.center.app.status.StateRequest;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.instcenter.ActivationModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.RequestAccountDO;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> at 2022/10/9 20:30
 **/
@Service
@Slf4j
public class OfflineActivationTemplate implements SubAccountActivationTemplate {

    @Autowired
    DingAlertClient dingAlertClient;
    @Autowired
    StateMachineExecutor stateMachineExecutor;

    @Override
    public void activation(RequestAccountDO requestAccountDO) {

        InstFundsAccountEntity instFundsAccountEntity = requestAccountDO.getInstFundsAccountEntity();
        InstSubFundsAccountEntity instSubFundsAccountEntity = requestAccountDO.getInstSubFundsAccountEntity();
        // 同步数据中心(数据中心处理), 同步成功更改状态为（激活中）
        if (true) {
            // 更改状态为激活中
            this.updateSubAccountStatusToApply(instFundsAccountEntity, instSubFundsAccountEntity);
        }
        try {
            // 发送钉钉通知
            String title = "机构子级账号【线下激活】";
            String message = String.format("\n- 机构账号名称：%s\n- 机构编码：%s\n- 使用类型：%s\n- 子级账号：%s\n- 子级账号名称：%s\n- 申请商户：%s\n- 应用场景：%s"
                    , instFundsAccountEntity.getAccountName(), instFundsAccountEntity.getInstCode(), instSubFundsAccountEntity.getSubUseType(), instSubFundsAccountEntity.getSubAccountNo(), instSubFundsAccountEntity.getSubAccountName(), instSubFundsAccountEntity.getMerchantNo(), instSubFundsAccountEntity.getScenes());
            dingAlertClient.sendMsgForGroupSubAccount(CommonConstants.PUSH_SUB_ACCOUNT_HANDLE_GROUP, title, message);
        } catch (Exception e) {
            log.info("OfflineActivationTemplate-activation sendMsg Exception:{}", e);
        }
    }

    /**
     * 更改状态为激活中
     **/
    public void updateSubAccountStatusToApply(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountEntity instSubFundsAccountEntity) {
        // 更改状态为激活中
        StateRequest stateRequest = new StateRequest(instSubFundsAccountEntity.getStatus(), ActivationModeEnum.getByType(instFundsAccountEntity.getActivationMode()), instSubFundsAccountEntity);
        instSubFundsAccountEntity.setStatus(SubAccountStatusEnum.ACTIVE.getStatus());
        stateMachineExecutor.transChangeSubAccountActivationState(stateRequest);

    }

}
