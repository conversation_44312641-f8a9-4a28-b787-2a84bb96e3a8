package com.payermax.channel.inst.center.app.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.infrastructure.entity.InstAccountEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstAccountDao;
import com.payermax.channel.inst.center.app.service.InstAccountService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 机构账号Service实现
 *
 * <AUTHOR>
 * @date 2022/6/4 14:20
 */
@Service
public class InstAccountServiceImpl implements InstAccountService {

    @Autowired
    private InstAccountDao instAccountDao;

    @Override
    public int save(InstAccountEntity record) {
        Preconditions.checkArgument(record != null, "param record is mandatory");

        int result = 0;
        if (record.getId() == null) {
            // 新增
            result = instAccountDao.insert(record);
        } else {
            // 更新
            result = instAccountDao.updateByPrimaryKey(record);
        }
        return result;
    }

    @Override
    public int delete(Long requirementOrderId, Long instId, String env, List<Long> ids) {
        Preconditions.checkArgument(requirementOrderId != null, "param requirementOrderId is mandatory");
        Preconditions.checkArgument(instId != null, "param instId is mandatory");
        Preconditions.checkArgument(StringUtils.isNotBlank(env), "param env is mandatory");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(ids), "param ids is mandatory");

        int result = instAccountDao.delete(requirementOrderId, instId, env, ids);
        return result;
    }

    @Override
    public List<InstAccountEntity> queryList(InstAccountEntity queryEntity) {
        Preconditions.checkArgument(queryEntity != null, "param queryEntity is mandatory");

        List<InstAccountEntity> entityList = instAccountDao.selectAll(queryEntity);
        return entityList;
    }
}
