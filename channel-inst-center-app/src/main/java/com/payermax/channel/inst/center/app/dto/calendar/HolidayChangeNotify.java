package com.payermax.channel.inst.center.app.dto.calendar;

import com.payermax.channel.inst.center.facade.response.calendar.HolidayChangeItem;
import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR> t<PERSON>
 * @version 2024/12/9 11:24
 */
@Getter
@Setter
@ToString
public class HolidayChangeNotify extends BaseMqMessage {

    /**
     * 通知类型
     * - REALTIME 单次变更立即通知
     * - DAILY    单日所有变更汇总通知
     */
    private String notifyType;

    /**
     * 变更具体信息
     */
    private List<HolidayChangeItem> holidayChangeItems;
}
