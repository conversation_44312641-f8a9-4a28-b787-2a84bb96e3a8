package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstProductFeeEntity;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/31 10:20
 */
public interface InstProductFeeService {
    /**
     * 保存产品费用
     *
     * @param records
     * @return
     */
    int saveAll(List<InstProductFeeEntity> records);

    /**
     * 根据合同单号和产品编码查询产品费用
     *
     * @param contractNo
     * @return
     */
    List<InstProductFeeEntity> getByContractNoAndProductCodes(String contractNo,List<String> productCodes);

    /**
     * 删除产品费用
     * @param contractNo,productCode
     * @return
     */
    int deleteByContractNoAndProductCode(String contractNo,String productCode);
}
