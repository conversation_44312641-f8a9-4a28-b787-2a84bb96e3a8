package com.payermax.channel.inst.center.app.manage.contract.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowCallbackHandler;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.response.InstContractAiParseDTO;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.LogScenesTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.model.contract.ContractAiParse;
import com.payermax.channel.inst.center.common.utils.AsyncTaskUtils;
import com.payermax.channel.inst.center.common.utils.ListUtils;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.adapter.DifyHttpAdapter;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.payermax.channel.inst.center.infrastructure.util.IDGenerator;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.request.ProcessRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @DESC
 */
@Slf4j
@Service
@RequiredArgsConstructor
@WorkflowCallbackHandler(businessType = BusinessTypeEnum.INST_CENTER, moduleName = OperateModuleEnum.INST_CENTER_CONTRACT_MANAGER, actionType = OperateTypeEnum.ADD)
public class InstContractAiParseHandler extends AbstractWorkflowHandler{

    private final DifyHttpAdapter difyHttpAdapter;
    private final InstBusinessDraftRepository businessDraftRepository;
    private final IDGenerator idGenerator;
    private final AsyncTaskUtils asyncTaskUtils;
    private final InstBusinessDraftAssembler draftAssembler;

    @NacosValue(value = "${inst.contract.ai.parse.needRetry:true}", autoRefreshed = true)
    private Boolean needRetry;
    @NacosValue(value = "${inst.contract.ai.parse.maxRetryCount:5}", autoRefreshed = true)
    private int maxRetryCount;


    /**
     * 合约解析受理
     */
    public String acceptAiParse(InstContractParseRequestDTO request) {
        // 受理
        InstContractAiParseDTO dto = new InstContractAiParseDTO().setRequest(request);
        String draftId = idGenerator.generateIdAccordingToSystem(BusinessTypeEnum.INST_CENTER, LogScenesTypeEnum.CONTRACT_PARSE);
        String businessKey = ListUtils.quickBuildUnderlineKey(request.getInstCode(), request.getContractEntity(), request.getEffectiveTime().getTime());
        // 落库
        InstBusinessDraft draft = draftAssembler.buildInitDraft(draftId,
                OperateModuleEnum.INST_CENTER_CONTRACT_MANAGER, OperateTypeEnum.ADD, businessKey, JSON.toJSONString(dto), request.getOperator());
        businessDraftRepository.save(draftAssembler.domain2Po(draft));
        log.info("受理成功: {}", draftId);

        // 开始异步解析
        log.info("开始异步解析");
        asyncTaskUtils.instCenterAsyncTaskExecutor("InstContractAiParse", draftId, this::parseAndSave);

        return draftId;
    }

    /**
     * 解析并保存结果
     */
    public Boolean parseAndSave(String draftId) {
        // 获取草稿
        InstBusinessDraft draft = draftAssembler.po2Domain(businessDraftRepository.getById(draftId));
        AssertUtil.isTrue(!draft.isFinalStatus(), ErrorCodeEnum.INST_CENTER_CONTRACT_AI_PARSE_ERROR.getCode(), String.format("草稿已到达终态: %s, %s", draftId, draft.getStatus()));
        InstContractAiParseDTO parseDTO = JSON.parseObject(draft.getDraftData(), InstContractAiParseDTO.class);
        log.info("{}", draft);

        try{
            // 开始解析
            log.info("开始解析");
            contractAiParse(parseDTO);
            // 更新草稿
            draft.setStatus(InstProcessStatusEnum.PASS);
            draft.setDraftData(JSON.toJSONString(parseDTO));
            businessDraftRepository.updateById(draftAssembler.domain2Po(draft));
        }catch (Exception e){
            log.error("合约 AI 解析失败: {}", e.getMessage());
            businessDraftRepository.handleRetryOrAbort(draftAssembler.domain2Po(draft), needRetry, maxRetryCount);
        }
        return Boolean.TRUE;
    }


    /**
     * AI 合约解析
     */
    public void contractAiParse(InstContractAiParseDTO parseDTO) {
        long startTime = System.currentTimeMillis();

        ContractAiParse.ContentSimplify contentSimplify = difyHttpAdapter.contractContentSimplify(parseDTO.getRequest().getContractFeeFile());
        log.info("调用 DIFY 工作流简化合约内容: {}", contentSimplify);

        ContractAiParse.CurrencyExtract currencyExtractMsg = difyHttpAdapter.contractCurrencyExtract(contentSimplify.getText());
        log.info("调用 DIFY 工作流提取币种信息: {}", currencyExtractMsg);

        List<ContractAiParse.FeeItem> feeItemList = currencyExtractMsg.getCcyList().parallelStream()
                .flatMap(ccy -> difyHttpAdapter.currencyFeeParse(contentSimplify.getText(), ccy).stream())
                .collect(Collectors.toList());
        log.info("调用 DIFY 工作流提取币种费用: {}", JSON.toJSONString(feeItemList));

        long costTime = (System.currentTimeMillis() - startTime) / 1000;
        log.info("合约 AI 解析耗时: {}s", costTime);

        parseDTO.setContractFeeContent(contentSimplify.getText());
        parseDTO.setCurrencyList(currencyExtractMsg.getCcyList());
        parseDTO.setFeeItemList(feeItemList);
        parseDTO.setParseCostTime(costTime);

    }

    @Override
    public Boolean retryHandler(InstBusinessDraft draft){
        retryPreCheck(draft);
        log.info("开始重试");
        parseAndSave(draft.getDraftId());
        return Boolean.TRUE;
    }

    @Override
    protected ProcessRequest.ProcessStart fillCustomProcessConfig(InstBusinessDraft draft, ProcessRequest.ProcessStart processConfig) {
        log.info("空实现");
        return null;
    }

    @Override
    protected Boolean processPassCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("空实现");
        return null;
    }

    @Override
    protected Boolean processRejectCallbackHandler(InstBusinessDraft draft, WfProcessEventInfo eventInfo) {
        log.info("空实现");
        return null;
    }
}
