package com.payermax.channel.inst.center.app.request.contract;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @DESC
 */
@Data
public class InstContractAiParseQueryRequest {

    private String draftId;

    private String instCode;

    private String contractEntity;

    private String status;

    @JsonFormat(pattern="yyyy-MM-dd hh:mm")
    private Date effectiveStartTime;

    @JsonFormat(pattern="yyyy-MM-dd hh:mm")
    private Date effectiveEndTime;

    @JsonFormat(pattern="yyyy-MM-dd hh:mm")
    private Date startTime;

    @JsonFormat(pattern="yyyy-MM-dd hh:mm")
    private Date endTime;
}
