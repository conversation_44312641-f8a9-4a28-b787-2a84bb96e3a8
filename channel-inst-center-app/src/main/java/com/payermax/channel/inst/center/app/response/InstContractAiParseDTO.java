package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.common.model.contract.ContractAiParse;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28
 * @DESC
 */
@Data
@Accessors(chain = true)
public class InstContractAiParseDTO {


    /**
     * 请求参数
     */
    private InstContractParseRequestDTO request;


    /**
     * 合约费用相关内容
     */
    private String contractFeeContent;

    /**
     * 币种列表
     */
    private List<String> currencyList;

    /**
     * 费用列表
     */
    private List<ContractAiParse.FeeItem> feeItemList;

    /**
     * 解析耗时
     */
    private Long parseCostTime;

}
