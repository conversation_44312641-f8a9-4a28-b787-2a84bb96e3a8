package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstAccountEntity;

import java.util.List;

/**
 * 机构账号Service
 *
 * <AUTHOR>
 * @date 2022/6/4 14:05
 */
public interface InstAccountService {

    /**
     * 保存
     *
     * @param record
     * @return
     */
    int save(InstAccountEntity record);

    /**
     * 删除集成单指定环境下的非ids的账号
     *
     * @param ids
     * @return
     */
    int delete(Long requirementOrderId, Long instId, String env, List<Long> ids);

    /**
     * 查询集合
     *
     * @param queryEntity
     * @return
     */
    List<InstAccountEntity> queryList(InstAccountEntity queryEntity);

}
