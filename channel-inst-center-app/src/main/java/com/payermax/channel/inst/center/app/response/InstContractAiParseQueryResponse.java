package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.common.model.contract.ContractAiParse;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27
 * @DESC
 */
@Data
public class InstContractAiParseQueryResponse {


    /**
     * 草稿 ID
     */
    private String draftId;

    /**
     * 机构编码
     * */
    private String instCode;

    /**
     * 合同主体
     * */
    private String contractEntity;


    /**
     * 合同生效时间
     * */
    private Date effectiveTime;

    /**
     * 合同费用文件地址
     * */
    private String contractFeeFile;

    /**
     * 合同编号
     * */
    private String contractNo;

    /**
     * 合同影印件地址
     * */
    private List<String> photocopyUrl;

    /**
     * 备注
     * */
    private String remark;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 状态
     */
    private String status;



    /**
     * 合约费用相关内容
     */
    private String contractFeeContent;

    /**
     * 币种列表
     */
    private List<String> currencyList;

    /**
     * 费用列表
     */
    private List<ContractAiParse.FeeItem> feeItemList;

    /**
     * 解析耗时
     */
    private Long parseCostTime;

}
