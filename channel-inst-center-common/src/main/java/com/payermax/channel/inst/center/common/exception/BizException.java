package com.payermax.channel.inst.center.common.exception;

import lombok.Getter;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;

@Getter
public class BizException extends RuntimeException {

  private ErrorCodeEnum errorCode;

  private Object data;

  public BizException(ErrorCodeEnum errorCode) {
    super(errorCode.getMsg());
    this.errorCode = errorCode;
  }

  public BizException(ErrorCodeEnum errorCode, Throwable throwable) {
    super(errorCode.getMsg(), throwable);
    this.errorCode = errorCode;
  }

  public BizException(ErrorCodeEnum errorCode, String errorMsg) {
    super(errorMsg);
    this.errorCode = errorCode;
  }

  @Override
  public String toString() {
    return "BizException{" +
            "code=" + errorCode.getCode() +
            ", msg='" + errorCode.getMsg() + '\'' +
            ", data=" + data +
            '}';
  }
}
