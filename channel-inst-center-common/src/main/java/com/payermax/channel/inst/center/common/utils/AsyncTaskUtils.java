package com.payermax.channel.inst.center.common.utils;

import com.payermax.channel.inst.center.common.constants.CommonConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2025/5/12
 * @DESC
 */
@Slf4j
@Component
public class AsyncTaskUtils {

    /**
     * 异步处理任务
     */
    @Async(CommonConstants.INST_CENTER_ASYNC_TASK_EXECUTOR_NAME)
    public <T,R> CompletableFuture<R> instCenterAsyncTaskExecutor(String taskName, T input, Function<T, R> taskLogic) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始异步处理任务: {}, {}", taskName, input);
                R result = taskLogic.apply(input);
                log.info("异步任务处理完成");
                return result;
            } catch (Exception e) {
                log.error("异步任务处理失败: {}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        });
    }
}
