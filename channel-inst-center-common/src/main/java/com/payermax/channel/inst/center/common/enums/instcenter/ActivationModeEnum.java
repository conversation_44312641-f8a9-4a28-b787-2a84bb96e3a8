package com.payermax.channel.inst.center.common.enums.instcenter;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/10/8 17:05
 **/
public enum ActivationModeEnum {
    
    API("API", "API模式"),
    OFFLINE("OFFLINE", "线下模式");

    private String type;

    private String desc;

    ActivationModeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static ActivationModeEnum getByType(String type) {
        for (ActivationModeEnum subAccountModeEnum : ActivationModeEnum.values()) {
            if (Objects.equals(subAccountModeEnum.getType(),type)) {
                return subAccountModeEnum;
            }
        }
        return null;
    }

}
