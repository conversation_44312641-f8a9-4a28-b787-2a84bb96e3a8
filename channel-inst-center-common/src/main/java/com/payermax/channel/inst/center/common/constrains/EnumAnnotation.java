package com.payermax.channel.inst.center.common.constrains;

import com.payermax.channel.inst.center.common.enums.IDescEnum;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * <AUTHOR> at 2023/6/11 10:46 AM
 **/
@Constraint(validatedBy = EnumAnnotationValidator.class)
@Target({java.lang.annotation.ElementType.FIELD})
@Retention(java.lang.annotation.RetentionPolicy.RUNTIME)
@Documented
public @interface EnumAnnotation {

    String message() default "data not valid";

    Class<? extends IDescEnum> values();

    //下面这两个属性必须添加
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
