package com.payermax.channel.inst.center.common.enums.instcenter;

/**
 * 申请阶段枚举
 *
 * <AUTHOR>
 * @date 2022/5/24 23:11
 */
public enum ApplyStageEnum {

    /**
     * 申请
     */
    APPLY(new String[]{ApplyStatusEnum.AUDIT_AGREE.name()}),
    /**
     * NDA审核
     */
    NDA(new String[]{NdaStatusEnum.COMPLETED.name()}),
    /**
     * DD审核
     */
    DD(new String[]{DdStatusEnum.AUDIT_AGREE.name(), DdStatusEnum.CONDITIONAL_AGREE.name()}),
    /**
     * KYC审核
     */
    KYC(new String[]{KycStatusEnum.AUDIT_AGREE.name()}),
    /**
     * 合同签订
     */
    CONTRACT(new String[]{ContractStatusEnum.AUDIT_AGREE.name()}),
    /**
     * 集成需求
     */
    REQUIREMENT(new String[]{RequirementOrderStatusEnum.PD_ANALYSIS.name()}),
    /**
     * 集成需求排期
     */
    REQUIREMENT_SCHEDULE(new String[]{});

    public String[] finalStatus;

    ApplyStageEnum(String[] finalStatus) {
        this.finalStatus = finalStatus;
    }

    /**
     * 是否是终态
     *
     * @param status
     * @return
     */
    public boolean isFinalStatus(String status) {
        for (String tmp : this.finalStatus) {
            if (tmp.equals(status)) {
                return true;
            }
        }
        return false;
    }
}
