package com.payermax.channel.inst.center.common.enums;

import java.util.Objects;

/**
 * <AUTHOR> at 2022/9/9 10:40
 **/
public enum IsSupportEnum {

    NULL(null, "空"),
    NOT_SUOOPRT((byte) 0, "不支持"),
    SUOOPRT((byte) 1, "支持");

    private Byte type;

    private String desc;

    IsSupportEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Byte getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static IsSupportEnum getByType(Byte type) {
        for (IsSupportEnum isSupportEnum : IsSupportEnum.values()) {
            if (Objects.equals(isSupportEnum.getType(),type)) {
                return isSupportEnum;
            }
        }
        return IsSupportEnum.NULL;
    }
}
