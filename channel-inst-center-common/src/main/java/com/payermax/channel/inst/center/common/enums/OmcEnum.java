package com.payermax.channel.inst.center.common.enums;

import lombok.Getter;

public interface OmcEnum {

    String getCode();

    String getMessage();

    @Getter
    enum SysEnum implements OmcEnum {
        OK("200", "OK!"),
        OK2("0000", "OK!"),
        FAILED("400", "Failed!"),
        CHECK_FAILED("401", "Parameter check failed!"),
        NAME_EXIST("402", "Name already exist!"),
        RESULT_ERROR("403", "Response object please use BaseResult!"),
        NOT_FOUND("404", "Not found!"),
        FEIGN_ERROR("405","Remote call failed!"),
        REPEAT_SUBMIT("406", "Please do not resubmit!"),
        UNABLE_LOCK("407","Unable to lock!"),
        CURRENT_LIMITING("408","Request too frequently, please try later!"),
        TRADE_SUCCESS_CAN_START("409", "Only when the deal is successful can disputes be initiated"),
        TIME_OUT("410", "Time Out!"),
        RELEASE_LOCK_ERROR("411", "Release lock error"),
        ACCOUNT_ADD_FAILED("omc1001","Account add failed!");

        private String code;
        private String message;

        SysEnum(String code, String message) {
            this.code = code;
            this.message = message;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getMessage() {
            return message;
        }
    }

    @Getter
    enum UserEnum implements OmcEnum {
        LOGIN_OK("200", "Login success!"),
        LOGOUT_OK("200", "Logout success!"),
        KICK_OUT("300", "You have already logged in elsewhere, please login again!"),
        LOGIN_FAILED("301","Incorrect user name or password, please login again!"),
        PERMISSION_DENIED("303","Permission denied!"),
        SHAREIT_SYSTEM_AUTH_FAILED("304", "SHAREit认证系统异常，请稍后重试"),
        LOSE_TOKEN("502", "Please login to get x-token!");

        private String code;
        private String message;

        UserEnum(String code, String message) {
            this.code = code;
            this.message = message;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getMessage() {
            return message;
        }
    }

    @Getter
    enum DisputeEnum implements OmcEnum {
        DISPUTE_PROCESSING("2001", "该订单已经立案或已发起新案件"),;

        private String code;
        private String message;

        DisputeEnum(String code, String message) {
            this.code = code;
            this.message = message;
        }

        @Override
        public String getCode() {
            return code;
        }

        @Override
        public String getMessage() {
            return message;
        }
    }
}
