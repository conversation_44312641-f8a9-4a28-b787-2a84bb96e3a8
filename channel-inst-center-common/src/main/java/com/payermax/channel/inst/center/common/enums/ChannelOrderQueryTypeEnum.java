package com.payermax.channel.inst.center.common.enums;

/**
 * 渠道单查询类型枚举
 * 0-业务单号
 * 1-渠道请求单号
 * 2-渠道提交单号3
 * 3-三方机构订单号
 * 4-四方机构单号
 */
public enum ChannelOrderQueryTypeEnum {
    BIZ_ORDER_NO(0, "业务单号"),
    CHANNEL_PAY_REQUEST_NO(1, "渠道申请单号"),
    CHANNEL_PAY_COMMIT_NO(2, "渠道提交单号"),
    THIRD_ORG_ORDER_NO(3, "三方机构单号"),
    FOURTH_ORG_ORDER_NO(4, "四方机构单号"),
    CHANNEL_AUTH_ORDER_NO(5, "四方机构单号")
    ;


    private final Integer code;
    private final String desc;

    ChannelOrderQueryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public final static boolean hasCode(Integer code) {
        boolean hasCode = false;
        for (ChannelOrderQueryTypeEnum r : ChannelOrderQueryTypeEnum.values()) {
            if (r.getCode().equals(code)) {
                hasCode = true;
                break;
            }
        }
        return hasCode;
    }
}
