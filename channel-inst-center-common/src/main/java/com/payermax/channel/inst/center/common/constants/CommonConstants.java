package com.payermax.channel.inst.center.common.constants;

public class CommonConstants {

    public static final String QUERY_ALL = "0";

    public static final String COMMA = ",";

    public static final String BAR = "-";

    public static final String POND = "#";

    public static final String COLON = ":";

    public static final String YYYY_MM_DD_PATTERN = "yyyy-MM-dd";

    public static final String DETAULF_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_CN = "日期";

    public static final String STATEMENT_CN = "注：本邮件中的报表数据均默认按UTC时间统计";

    public static final String STATEMENT_EN = "Note: All the data metrics are calculated in UTC timezone";

    public static final String SINGLE_QUOTE = "'";

    public static final String SINGLE_QUOTE_COMMA = "','";

    public static final String ALL = "ALL";

    public static final String STAR = "*";

    public static final String OMC_EXCHANGE = "omcExchange";

    public static final String AUTO_ROUTE = "autoRoute";

    public static final String SHARE_ID = "shareId";

    public static final String WORKFLOW_TITLE = "标题";

    public static final String WORKFLOW_CONTENT_JSON = "类型";

    public static final String WORKFLOW_CONTENT_TEXT = "申请内容";

    public static final String PUSH_SUB_ACCOUNT_EXCEPTION_GROUP = "push-sub-account-exceptionNotify";

    public static final String PUSH_SUB_ACCOUNT_HANDLE_GROUP = "push-sub-account-handleNotify";

    public static final String ASYNC_TASK_EXECUTOR_NAME = "asyncServiceExecutor";

    public static final String INST_CENTER_ASYNC_TASK_EXECUTOR_NAME = "instCenterAsyncServiceExecutor";

    public static final String INST_SUB_ACCOUNT_NAME_RULE = "subAccountNameRule";

    public static final String PUSH_FX_SETTLE_QUERY_RESULT_GROUP = "push-fx-settle-query-result-handleNotify";

    public static final String FX_SETTLE_QUERY = "结算换汇信息查询";

    public static final String ENUM_DESC_CONFIG_PREFIX = "enum.desc";

    public static final String CARD_PAY_CODE = "CARDPAY";

    public static final String INST_NEW_QUERY_ERROR = "INST_NEW_QUERY_ERROR";

    public static final String SWIFT_CLEAR_SYSTEM = "SWIFT";
    
}
