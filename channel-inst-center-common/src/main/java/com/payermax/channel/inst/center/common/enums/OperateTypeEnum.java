package com.payermax.channel.inst.center.common.enums;
import lombok.Getter;

/**
 * 操作类型
 */
public enum OperateTypeEnum {

    SINGLE("SINGLE", "单笔"),
    BATCH("BATCH", "批量");

    @Getter
    private String value;

    @Getter
    private String desc;

    OperateTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static OperateTypeEnum getByValue(String value) {
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (operateTypeEnum.getValue().equals(value)) {
                return operateTypeEnum;
            }
        }
        return null;
    }
    public static OperateTypeEnum getByDesc(String desc) {
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (operateTypeEnum.getDesc().equals(desc)) {
                return operateTypeEnum;
            }
        }
        return null;
    }
}

