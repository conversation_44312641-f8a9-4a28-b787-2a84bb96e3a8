package com.payermax.channel.inst.center.common.utils;

import com.payermax.channel.inst.center.common.enums.OmcEnum;
import com.payermax.channel.inst.center.common.exception.CustomException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

/**
 * FileUtil
 *
 * <AUTHOR>
 * @desc
 */
@Slf4j
public class FileUtil {

    public static FileInfo download(String url) {
        if(StringUtils.isEmpty(url)) {
            throw new CustomException(OmcEnum.SysEnum.FAILED.getCode(), "Fail to download file, url is empty");
        }

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<byte[]> responseEntity;
        try {
            responseEntity = restTemplate.getForEntity(new URI(url), byte[].class);
        }catch (Exception e) {
            String errorMsg = "Fail to download file, url[" + url + "]";
            log.error(errorMsg, e);
            throw new CustomException(OmcEnum.SysEnum.FAILED.getCode(), errorMsg);
        }

        if(responseEntity.getStatusCode().value() == HttpStatus.OK.value()) {
            ContentDisposition contentDisposition = responseEntity.getHeaders().getContentDisposition();
            long fileSize = responseEntity.getHeaders().getContentLength();
            int i = url.indexOf('?');
            if(i != -1) {
                url = url.substring(0, url.indexOf('?'));
            }
            String fileName = url.substring(url.lastIndexOf('/') + 1);
            try {
                fileName = URLDecoder.decode(fileName, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
            }
            fileName = fileName.replaceFirst("/", "");
            String fileType = "";
            i = fileName.lastIndexOf(".");
            if(i != -1 && i != fileName.length()) {
                fileType = fileName.substring(i + 1).toLowerCase();
            }
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(StringUtils.isEmpty(contentDisposition.getFilename()) ? fileName : contentDisposition.getFilename());
            fileInfo.setFileSize(fileSize);
            fileInfo.setFileType(StringUtils.isEmpty(contentDisposition.getType()) ? fileType : contentDisposition.getType());
            fileInfo.setData(responseEntity.getBody());

            return fileInfo;
        }

        throw new CustomException(OmcEnum.SysEnum.FAILED.getCode(), "Fail to download file, url[" + url + "], response[" + new String(responseEntity.getBody(), StandardCharsets.UTF_8) + "]");
    }

    @Data
    public static class FileInfo {

        private String fileName;

        private String fileType;

        private Long fileSize;

        private byte[] data;
    }

}
