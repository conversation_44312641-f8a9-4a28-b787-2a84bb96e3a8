package com.payermax.channel.inst.center.common.utils;

import com.google.common.base.Preconditions;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2023/6/9 6:03 PM
 **/
@Component
public class ApplicationUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    public static <E extends Enum, R extends ISupportedScenarios<E>> R getBean(Class<R> classType, E scenario) {
        Preconditions.checkNotNull(classType, "classType is Null");
        Preconditions.checkNotNull(scenario, "scenario is Null");

        Map<String, R> beanMap = applicationContext.getBeansOfType(classType);

        List<R> beans = beanMap.values().stream().filter(item -> CollectionUtils.isNotEmpty(item.supportedScenarios())
                && item.supportedScenarios().contains(scenario)).collect(Collectors.toList());

        return getBean(beans);
    }

    private static <T> T getBean(List<T> beans) {
        if (CollectionUtils.isEmpty(beans)) {
            throw new BizException(ErrorCodeEnum.NOTHING_FOUND_BUT_EXPECT_ONE);
        }
        if (CollectionUtils.size(beans) > 1) {
            throw new BizException(ErrorCodeEnum.MORE_THAN_ONE);
        }
        return beans.get(0); //CHECKED
    }

    public static <T> T getBean(Class<T> classType) {
        Preconditions.checkNotNull(classType, "classType is Null");
        Map<String, T> beanMap = applicationContext.getBeansOfType(classType);
        List<T> beans = beanMap.values().stream().collect(Collectors.toList());
        return getBean(beans);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ApplicationUtils.applicationContext = applicationContext;
    }
}
