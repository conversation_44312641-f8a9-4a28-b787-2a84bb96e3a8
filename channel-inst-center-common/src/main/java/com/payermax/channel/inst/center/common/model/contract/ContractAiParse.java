package com.payermax.channel.inst.center.common.model.contract;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/27
 * @DESC
 */
@Data
public class ContractAiParse {

    private ContentSimplify contentSimplify;
    private CurrencyExtract currencyExtract;
    private CurrencyFees currencyFees;


    @Data
    public static class ContentSimplify{

        private String validate;
        private String text;
    }

    @Data
    public static class CurrencyExtract{
        private List<String> ccyList;
        private String ccyCount;
        private String parseSize;
    }

    @Data
    public static class CurrencyFees{
        private List<FeeItem> fees;
    }

    @Data
    public static class FeeItem{
        private String feeType;
        private String businessType;
        private String paymentMethodType;
        private String payCurrency;
        private String settleCurrency;
        private String country;
        private String contractFxSpread;
        private String currencyExchangeTime;
        private String roundingScale;
        private String roundingMode;
        private String feeDeductCurrency;
        private String calculateType;
        private String feeRateValue;
        private String percentMinAmount;
        private String percentMaxAmount;
        private String percentMinAmountCurrency;
        private String percentMaxAmountCurrency;
        private String feeValue;
        private String feeCurrency;
        private List<FeeStepCombineConfig> stepPercentAmount = new ArrayList<>();
        private String remark;
        private Map<String,Object> extendFields;
    }

    @Data
    public static class FeeStepCombineConfig{
        private String leftAmount;
        private String rightAmount;
        private String calculateType;
        private String feeRateValue;
        private String percentMinAmount;
        private String percentMaxAmount;
        private String percentMinAmountCurrency;
        private String percentMaxAmountCurrency;
        private String feeValue;
        private String feeCurrency;
    }
}
