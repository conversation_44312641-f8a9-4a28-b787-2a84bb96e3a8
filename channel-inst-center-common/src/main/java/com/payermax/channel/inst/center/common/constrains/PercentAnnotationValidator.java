package com.payermax.channel.inst.center.common.constrains;

import com.payermax.common.lang.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * <AUTHOR> at 2023/6/14 8:44 PM
 *
 * excel字段中包含多个百分比字符串，类似 1.5% ，通过正则表达式来判断其是否符合格式
 **/
@Slf4j
public class PercentAnnotationValidator implements ConstraintValidator<PercentAnnotation, String> {

    private static final String PATTERN = "-?(\\d+)(.\\d+)?%";

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if(StringUtil.isEmpty(value)) {
            return true;
        }
        if (checkValid(value, true)) {
            return true;

        } else {
            log.warn("{} 不符合数字百分比 Pattern:{}", value, PATTERN);
            return false;
        }
    }

    public static boolean checkValid(String value, boolean defaultWhenBlank) {
        if (StringUtils.isBlank(value)) {
            return defaultWhenBlank;
        }

        return Pattern.matches(PATTERN, value);
    }
}
