package com.payermax.channel.inst.center.common.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR> at 2022/10/10 19:56
 **/
public class BigDecimalUtil {

    /**
     * 在BigDecimal前补0，并转换为字符串
     *
     * @param fill 补0个数
     * @return
     */
    public static String bigDecimal2StringFill0(int fill, BigDecimal bigDecimal) {
        String result = bigDecimal.stripTrailingZeros().toPlainString();
        if (fill <= 0) {
            return result;
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < fill; i++) {
            stringBuilder.append("0");
        }
        return stringBuilder.append(result).toString();

    }
}
